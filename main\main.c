#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "esp_mesh.h"
#include "esp_netif.h"

#include "mesh_handler.h"
#include "mqtt_client.h"
#include "data_router.h"

static const char *TAG = "MESH_MAIN";

void app_main(void)
{
    ESP_LOGI(TAG, "Starting ESP Mesh ThingsBoard Application");
    
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // Initialize TCP/IP stack
    ESP_ERROR_CHECK(esp_netif_init());
    
    // Initialize event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    // Initialize data router
    data_router_init();
    
    // Initialize mesh network
    mesh_init();
    
    ESP_LOGI(TAG, "Application initialized successfully");
}
