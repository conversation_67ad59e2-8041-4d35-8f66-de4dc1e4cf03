[1/1000] Generating project_elf_src_esp32.c
[2/1000] Generating C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp-idf/esp_system/ld/memory.ld linker script...
[3/1000] Generating C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp-idf/esp_system/ld/sections.ld.in linker script...
[4/1000] Generating ../../partition_table/partition-table.bin
Partition table binary generated. Contents:
*******************************************************************************
# ESP-IDF Partition Table

# Name, Type, SubType, Offset, Size, Flags

nvs,data,nvs,0x9000,24K,

phy_init,data,phy,0xf000,4K,

factory,app,factory,0x10000,1M,

storage,data,spiffs,0x110000,960K,

*******************************************************************************
[5/1000] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/util/ctrl_sock.c.obj
[6/1000] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_ws.c.obj
[7/1000] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_uri.c.obj
[8/1000] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_auth.c.obj
[9/1000] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_sess.c.obj
[10/1000] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_parse.c.obj
[11/1000] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_main.c.obj
[12/1000] Building C object esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/src/esp_https_ota.c.obj
[13/1000] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_txrx.c.obj
[14/1000] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_utils.c.obj
[15/1000] Linking C static library esp-idf\esp_https_ota\libesp_https_ota.a
[16/1000] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_header.c.obj
[17/1000] Linking C static library esp-idf\esp_http_server\libesp_http_server.a
[18/1000] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_socks_proxy.c.obj
[19/1000] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_internal.c.obj
[20/1000] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/packet.c.obj
[21/1000] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub_transport.c.obj
[22/1000] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport.c.obj
[23/1000] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ws.c.obj
[24/1000] Building ASM object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/xtensa/gdbstub-entry.S.obj
[25/1000] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ssl.c.obj
[26/1000] Building ASM object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/xtensa/xt_debugexception.S.obj
[27/1000] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/xtensa/gdbstub_xtensa.c.obj
[28/1000] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub.c.obj
[29/1000] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/esp_http_client.c.obj
[30/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali.c.obj
[31/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali_curve_fitting.c.obj
[32/1000] Linking C static library esp-idf\esp_http_client\libesp_http_client.a
[33/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/deprecated/esp_adc_cal_common_legacy.c.obj
[34/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/deprecated/esp32/esp_adc_cal_legacy.c.obj
[35/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/esp32/adc_cali_line_fitting.c.obj
[36/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_common.c.obj
[37/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_oneshot.c.obj
[38/1000] Linking C static library esp-idf\tcp_transport\libtcp_transport.a
[39/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_continuous.c.obj
[40/1000] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp-tls-crypto/esp_tls_crypto.c.obj
[41/1000] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/esp32/adc_dma.c.obj
[42/1000] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_platform_port.c.obj
[43/1000] Linking C static library esp-idf\esp_gdbstub\libesp_gdbstub.a
[44/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/lib_printf.c.obj
[45/1000] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_error_capture.c.obj
[46/1000] Linking C static library esp-idf\esp_adc\libesp_adc.a
[47/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/mesh_event.c.obj
[48/1000] Building C object esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/http_parser.c.obj
[49/1000] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls.c.obj
[50/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig.c.obj
[51/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default_ap.c.obj
[52/1000] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_mbedtls.c.obj
[53/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_netif.c.obj
[54/1000] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/coexist_debug_diagram.c.obj
[55/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_init.c.obj
[56/1000] Linking C static library esp-idf\esp-tls\libesp-tls.a
[57/1000] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/coexist_debug.c.obj
[58/1000] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/esp32/esp_coex_adapter.c.obj
[59/1000] Linking C static library esp-idf\http_parser\libhttp_parser.a
[60/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig_ack.c.obj
[61/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/esp32/esp_adapter.c.obj
[62/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/os_xtensa.c.obj
[63/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/eloop.c.obj
[64/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ap_config.c.obj
[65/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_1x.c.obj
[66/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/pmksa_cache_auth.c.obj
[67/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth_ie.c.obj
[68/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/sta_info.c.obj
[69/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_11.c.obj
[70/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth.c.obj
[71/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/comeback_token.c.obj
[72/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/dragonfly.c.obj
[73/1000] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default.c.obj
[74/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/bitfield.c.obj
[75/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/wpa_common.c.obj
[76/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae.c.obj
[77/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-siv.c.obj
[78/1000] Linking C static library esp-idf\esp_wifi\libesp_wifi.a
[79/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ccmp.c.obj
[80/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-kdf.c.obj
[81/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-gcm.c.obj
[82/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_group5.c.obj
[83/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tlsprf.c.obj
[84/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/crypto_ops.c.obj
[85/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ms_funcs.c.obj
[86/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_groups.c.obj
[87/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-tlsprf.c.obj
[88/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-tlsprf.c.obj
[89/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-prf.c.obj
[90/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-prf.c.obj
[91/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tprf.c.obj
[92/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-prf.c.obj
[93/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/md4-internal.c.obj
[94/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_common.c.obj
[95/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/chap.c.obj
[96/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/ieee802_11_common.c.obj
[97/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_common/eap_wsc_common.c.obj
[98/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_mschapv2.c.obj
[99/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap.c.obj
[100/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap.c.obj
[101/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls.c.obj
[102/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap_common.c.obj
[103/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls_common.c.obj
[104/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/mschapv2.c.obj
[105/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_ttls.c.obj
[106/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_common.c.obj
[107/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_pac.c.obj
[108/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast.c.obj
[109/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/pmksa_cache.c.obj
[110/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/base64.c.obj
[111/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa_ie.c.obj
[112/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/common.c.obj
[113/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa.c.obj
[114/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/ext_password.c.obj
[115/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/uuid.c.obj
[116/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpabuf.c.obj
[117/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/json.c.obj
[118/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpa_debug.c.obj
[119/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps.c.obj
[120/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_parse.c.obj
[121/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_process.c.obj
[122/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_build.c.obj
[123/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa2_api_port.c.obj
[124/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_common.c.obj
[125/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_dev_attr.c.obj
[126/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_enrollee.c.obj
[127/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae_pk.c.obj
[128/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa_main.c.obj
[129/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpas_glue.c.obj
[130/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_common.c.obj
[131/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_eap_client.c.obj
[132/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wps.c.obj
[133/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_owe.c.obj
[134/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa3.c.obj
[135/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_hostap.c.obj
[136/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/fastpbkdf2.c.obj
[137/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls.c.obj
[138/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c.obj
[139/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/rc4.c.obj
[140/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-wrap.c.obj
[141/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-unwrap.c.obj
[142/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/des-internal.c.obj
[143/1000] Linking C static library esp-idf\esp_coex\libesp_coex.a
[144/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/tls_mbedtls.c.obj
[145/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c.obj
[146/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-ccm.c.obj
[147/1000] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-ec.c.obj
[148/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_objects.c.obj
[149/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_handlers.c.obj
[150/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_defaults.c.obj
[151/1000] Linking C static library esp-idf\wpa_supplicant\libwpa_supplicant.a
[152/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip_defaults.c.obj
[153/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/esp_pbuf_ref.c.obj
[154/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_sntp.c.obj
[155/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/wlanif.c.obj
[156/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/ethernetif.c.obj
[157/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/sntp/sntp.c.obj
[158/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_lib.c.obj
[159/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_msg.c.obj
[160/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/err.c.obj
[161/1000] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip.c.obj
[162/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netbuf.c.obj
[163/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/if_api.c.obj
[164/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netdb.c.obj
[165/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netifapi.c.obj
[166/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/tcpip.c.obj
[167/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/sntp/sntp.c.obj
[168/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/def.c.obj
[169/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/netbiosns/netbiosns.c.obj
[170/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/inet_chksum.c.obj
[171/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/sockets.c.obj
[172/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/mem.c.obj
[173/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/init.c.obj
[174/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ip.c.obj
[175/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/dns.c.obj
[176/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/pbuf.c.obj
[177/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/sys.c.obj
[178/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/netif.c.obj
[179/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/stats.c.obj
[180/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/raw.c.obj
[181/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/memp.c.obj
[182/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_out.c.obj
[183/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_in.c.obj
[184/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/timeouts.c.obj
[185/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp.c.obj
[186/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/autoip.c.obj
[187/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/udp.c.obj
[188/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/icmp.c.obj
[189/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/etharp.c.obj
[190/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/dhcp.c.obj
[191/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/igmp.c.obj
[192/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_frag.c.obj
[193/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_napt.c.obj
[194/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_addr.c.obj
[195/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4.c.obj
[196/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ethip6.c.obj
[197/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/inet6.c.obj
[198/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/icmp6.c.obj
[199/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_frag.c.obj
[200/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_addr.c.obj
[201/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6.c.obj
[202/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/mld6.c.obj
[203/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ethernet.c.obj
[204/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/nd6.c.obj
[205/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif.c.obj
[206/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/slipif.c.obj
[207/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif_fdb.c.obj
[208/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/auth.c.obj
[209/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-new.c.obj
[210/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-md5.c.obj
[211/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ccp.c.obj
[212/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap_ms.c.obj
[213/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/demand.c.obj
[214/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eap.c.obj
[215/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ecp.c.obj
[216/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eui64.c.obj
[217/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/dhcp6.c.obj
[218/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/fsm.c.obj
[219/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/lcp.c.obj
[220/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipv6cp.c.obj
[221/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/magic.c.obj
[222/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/mppe.c.obj
[223/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/multilink.c.obj
[224/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ppp.c.obj
[225/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppapi.c.obj
[226/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppcrypt.c.obj
[227/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppoe.c.obj
[228/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppol2tp.c.obj
[229/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppos.c.obj
[230/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/upap.c.obj
[231/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/utils.c.obj
[232/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/vj.c.obj
[233/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/tcp_isn_default.c.obj
[234/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/lwip_default_hooks.c.obj
[235/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/debug/lwip_debug.c.obj
[236/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/sockets_ext.c.obj
[237/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/freertos/sys_arch.c.obj
[238/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/acd_dhcp_check.c.obj
[239/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/esp32xx/vfs_lwip.c.obj
[240/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/esp_ping.c.obj
[241/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping_sock.c.obj
[242/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping.c.obj
[243/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/arc4.c.obj
[244/1000] Linking C static library esp-idf\esp_netif\libesp_netif.a
[245/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/md5.c.obj
[246/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/sha1.c.obj
[247/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/des.c.obj
[248/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipcp.c.obj
[249/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/dhcpserver/dhcpserver.c.obj
[250/1000] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/nullfs.c.obj
[251/1000] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/lib_printf.c.obj
[252/1000] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_eventfd.c.obj
[253/1000] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_semihost.c.obj
[254/1000] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_common.c.obj
[255/1000] Building C object esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/vfs_console.c.obj
[256/1000] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_override.c.obj
[257/1000] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs.c.obj
[258/1000] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/esp32/phy_init_data.c.obj
[259/1000] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/btbb_init.c.obj
[260/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/esp32/dac_legacy.c.obj
[261/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/dac_common_legacy.c.obj
[262/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_legacy.c.obj
[263/1000] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_init.c.obj
[264/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_dma_legacy.c.obj
[265/1000] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/md4.c.obj
[266/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/timer_legacy.c.obj
[267/1000] Linking C static library esp-idf\lwip\liblwip.a
[268/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/sigma_delta_legacy.c.obj
[269/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/i2c/i2c.c.obj
[270/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/touch_sensor/touch_sensor_common.c.obj
[271/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/pcnt_legacy.c.obj
[272/1000] Linking C static library esp-idf\vfs\libvfs.a
[273/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/mcpwm_legacy.c.obj
[274/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/touch_sensor/esp32/touch_sensor.c.obj
[275/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/i2s_legacy.c.obj
[276/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/twai/twai.c.obj
[277/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/rmt_legacy.c.obj
[278/1000] Linking C static library esp-idf\esp_vfs_console\libesp_vfs_console.a
[279/1000] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_i2s_deprecated.c.obj
[280/1000] Linking C static library esp-idf\esp_phy\libesp_phy.a
[281/1000] Building C object esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_common.c.obj
[282/1000] Building C object esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_slave.c.obj
[283/1000] Building C object esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/src/sdm.c.obj
[284/1000] Linking C static library esp-idf\driver\libdriver.a
[285/1000] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_common.c.obj
[286/1000] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_encoder.c.obj
[287/1000] Building C object esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_master.c.obj
[288/1000] Building C object esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/src/ledc.c.obj
[289/1000] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_rx.c.obj
[290/1000] Building C object esp-idf/esp_driver_dac/CMakeFiles/__idf_esp_driver_dac.dir/dac_oneshot.c.obj
[291/1000] Linking C static library esp-idf\esp_driver_ledc\libesp_driver_ledc.a
[292/1000] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_tx.c.obj
[293/1000] Building C object esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_crc.c.obj
[294/1000] Building C object esp-idf/esp_driver_dac/CMakeFiles/__idf_esp_driver_dac.dir/dac_common.c.obj
[295/1000] Building C object esp-idf/esp_driver_dac/CMakeFiles/__idf_esp_driver_dac.dir/dac_cosine.c.obj
[296/1000] Linking C static library esp-idf\esp_driver_i2c\libesp_driver_i2c.a
[297/1000] Building C object esp-idf/esp_driver_dac/CMakeFiles/__idf_esp_driver_dac.dir/dac_continuous.c.obj
[298/1000] Building C object esp-idf/esp_driver_dac/CMakeFiles/__idf_esp_driver_dac.dir/esp32/dac_dma.c.obj
[299/1000] Linking C static library esp-idf\esp_driver_sdm\libesp_driver_sdm.a
[300/1000] Linking C static library esp-idf\esp_driver_rmt\libesp_driver_rmt.a
[301/1000] Building C object esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_transaction.c.obj
[302/1000] Building C object esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_host.c.obj
[303/1000] Building C object esp-idf/esp_driver_sdio/CMakeFiles/__idf_esp_driver_sdio.dir/src/sdio_slave.c.obj
[304/1000] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_init.c.obj
[305/1000] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_common.c.obj
[306/1000] Building C object esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/src/sdmmc_transaction.c.obj
[307/1000] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_cmd.c.obj
[308/1000] Linking C static library esp-idf\esp_driver_dac\libesp_driver_dac.a
[309/1000] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_io.c.obj
[310/1000] Building C object esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/src/sdmmc_host.c.obj
[311/1000] Linking C static library esp-idf\esp_driver_sdio\libesp_driver_sdio.a
[312/1000] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sd_pwr_ctrl/sd_pwr_ctrl.c.obj
[313/1000] Linking C static library esp-idf\esp_driver_sdspi\libesp_driver_sdspi.a
[314/1000] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_mmc.c.obj
[315/1000] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_sd.c.obj
[316/1000] Linking C static library esp-idf\esp_driver_sdmmc\libesp_driver_sdmmc.a
[317/1000] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_platform.c.obj
[318/1000] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_std.c.obj
[319/1000] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_pdm.c.obj
[320/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_cmpr.c.obj
[321/1000] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_common.c.obj
[322/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_com.c.obj
[323/1000] Linking C static library esp-idf\sdmmc\libsdmmc.a
[324/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_cap.c.obj
[325/1000] Linking C static library esp-idf\esp_driver_i2s\libesp_driver_i2s.a
[326/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_fault.c.obj
[327/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_gen.c.obj
[328/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_oper.c.obj
[329/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_sync.c.obj
[330/1000] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_dma.c.obj
[331/1000] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_timer.c.obj
[332/1000] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_common.c.obj
[333/1000] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_slave.c.obj
[334/1000] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_master.c.obj
[335/1000] Building C object esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/src/pulse_cnt.c.obj
[336/1000] Linking C static library esp-idf\esp_driver_mcpwm\libesp_driver_mcpwm.a
[337/1000] Linking C static library esp-idf\esp_driver_spi\libesp_driver_spi.a
[338/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition.cpp.obj
[339/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_lookup.cpp.obj
[340/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_cxx_api.cpp.obj
[341/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_locked.cpp.obj
[342/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_item_hash_list.cpp.obj
[343/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_storage.cpp.obj
[344/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_simple.cpp.obj
[345/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_platform.cpp.obj
[346/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_page.cpp.obj
[347/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_api.cpp.obj
[348/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_types.cpp.obj
[349/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_pagemanager.cpp.obj
[350/1000] Building C object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_bootloader.c.obj
[351/1000] Linking C static library esp-idf\esp_driver_pcnt\libesp_driver_pcnt.a
[352/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_manager.cpp.obj
[353/1000] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/default_event_loop.c.obj
[354/1000] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event_private.c.obj
[355/1000] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event.c.obj
[356/1000] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_init.c.obj
[357/1000] Building C object esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/ringbuf.c.obj
[358/1000] Building C object esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/src/gptimer.c.obj
[359/1000] Building C object esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/src/gptimer_common.c.obj
[360/1000] Building C object esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/src/uart_vfs.c.obj
[361/1000] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/system_time.c.obj
[362/1000] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer.c.obj
[363/1000] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/ets_timer_legacy.c.obj
[364/1000] Building C object esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/src/uart.c.obj
[365/1000] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_common.c.obj
[366/1000] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_lac.c.obj
[367/1000] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_exception_stubs.cpp.obj
[368/1000] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_init.cpp.obj
[369/1000] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_cond_var.c.obj
[370/1000] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread.c.obj
[371/1000] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_local_storage.c.obj
[372/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/abort.c.obj
[373/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/assert.c.obj
[374/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/heap.c.obj
[375/1000] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_rwlock.c.obj
[376/1000] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_semaphore.c.obj
[377/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/flockfile.c.obj
[378/1000] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_guards.cpp.obj
[379/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/poll.c.obj
[380/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/locks.c.obj
[381/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/getentropy.c.obj
[382/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/pthread.c.obj
[383/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/random.c.obj
[384/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/reent_init.c.obj
[385/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/termios.c.obj
[386/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/syscalls.c.obj
[387/1000] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_encrypted_partition.cpp.obj
[388/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/newlib_init.c.obj
[389/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/scandir.c.obj
[390/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/sysconf.c.obj
[391/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/realpath.c.obj
[392/1000] Linking C static library esp-idf\nvs_flash\libnvs_flash.a
[393/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/stdatomic.c.obj
[394/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/time.c.obj
[395/1000] Linking C static library esp-idf\esp_event\libesp_event.a
[396/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/heap_idf.c.obj
[397/1000] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/port/esp_time_impl.c.obj
[398/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_common.c.obj
[399/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/app_startup.c.obj
[400/1000] Linking C static library esp-idf\esp_driver_uart\libesp_driver_uart.a
[401/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_systick.c.obj
[402/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/list.c.obj
[403/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/queue.c.obj
[404/1000] Building ASM object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/portasm.S.obj
[405/1000] Linking C static library esp-idf\esp_ringbuf\libesp_ringbuf.a
[406/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/event_groups.c.obj
[407/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/timers.c.obj
[408/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c.obj
[409/1000] Linking C static library esp-idf\esp_driver_gptimer\libesp_driver_gptimer.a
[410/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/tasks.c.obj
[411/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/port.c.obj
[412/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/stream_buffer.c.obj
[413/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c.obj
[414/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/freertos_compatibility.c.obj
[415/1000] Linking C static library esp-idf\esp_timer\libesp_timer.a
[416/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions_event_groups.c.obj
[417/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/esp_cpu_intr.c.obj
[418/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj
[419/1000] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions.c.obj
[420/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/cpu_region_protect.c.obj
[421/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj
[422/1000] Linking C static library esp-idf\cxx\libcxx.a
[423/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/hw_random.c.obj
[424/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clk.c.obj
[425/1000] Linking C static library esp-idf\pthread\libpthread.a
[426/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/revision.c.obj
[427/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mac_addr.c.obj
[428/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_ctrl_os.c.obj
[429/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/intr_alloc.c.obj
[430/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modem.c.obj
[431/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_console.c.obj
[432/1000] Linking C static library esp-idf\newlib\libnewlib.a
[433/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_usb.c.obj
[434/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/periph_ctrl.c.obj
[435/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rtc_module.c.obj
[436/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_gpio_reserve.c.obj
[437/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_event.c.obj
[438/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/io_mux.c.obj
[439/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sar_periph_ctrl_common.c.obj
[440/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/regi2c_ctrl.c.obj
[441/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/esp_clk_tree.c.obj
[442/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp_clk_tree_common.c.obj
[443/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_dma_utils.c.obj
[444/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma_link.c.obj
[445/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_gpio.c.obj
[446/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modes.c.obj
[447/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_share_hw_ctrl.c.obj
[448/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_bus_lock.c.obj
[449/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/adc_share_hw_ctrl.c.obj
[450/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_utils.c.obj
[451/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rtc_wdt.c.obj
[452/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mspi_timing_tuning.c.obj
[453/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_wake_stub.c.obj
[454/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj
[455/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj
[456/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj
[457/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clock_output.c.obj
[458/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj
[459/1000] Linking C static library esp-idf\freertos\libfreertos.a
[460/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/cache_sram_mmu.c.obj
[461/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj
[462/1000] Building C object esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_crypto_lock.c.obj
[463/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/sar_periph_ctrl.c.obj
[464/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj
[465/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj
[466/1000] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj
[467/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj
[468/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj
[469/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj
[470/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj
[471/1000] Building C object esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/init.c.obj
[472/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj
[473/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj
[474/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/emac_periph.c.obj
[475/1000] Linking C static library esp-idf\esp_hw_support\libesp_hw_support.a
[476/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj
[477/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj
[478/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj
[479/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj
[480/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj
[481/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj
[482/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj
[483/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj
[484/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj
[485/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj
[486/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mpi_periph.c.obj
[487/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj
[488/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/twai_periph.c.obj
[489/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj
[490/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/wdt_periph.c.obj
[491/1000] Linking C static library esp-idf\esp_security\libesp_security.a
[492/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj
[493/1000] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj
[494/1000] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/port/esp32/memory_layout.c.obj
[495/1000] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/port/memory_layout_utils.c.obj
[496/1000] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_base.c.obj
[497/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_timestamp.c.obj
[498/1000] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_init.c.obj
[499/1000] Linking C static library esp-idf\soc\libsoc.a
[500/1000] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/multi_heap.c.obj
[501/1000] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps.c.obj
[502/1000] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/tlsf/tlsf.c.obj
[503/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj
[504/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/log_level.c.obj
[505/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/util.c.obj
[506/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_write.c.obj
[507/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/buffer/log_buffers.c.obj
[508/1000] Linking C static library esp-idf\heap\libheap.a
[509/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_lock.c.obj
[510/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/linked_list/log_linked_list.c.obj
[511/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/cache/log_binary_heap.c.obj
[512/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj
[513/1000] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/tag_log_level.c.obj
[514/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj
[515/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj
[516/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/color_hal.c.obj
[517/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj
[518/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj
[519/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj
[520/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_encrypt_hal_iram.c.obj
[521/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj
[522/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal.c.obj
[523/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_iram.c.obj
[524/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal_iram.c.obj
[525/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/rtc_io_hal.c.obj
[526/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/gpio_hal.c.obj
[527/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal.c.obj
[528/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/timer_hal.c.obj
[529/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/clk_tree_hal.c.obj
[530/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal.c.obj
[531/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal_iram.c.obj
[532/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal_iram.c.obj
[533/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal.c.obj
[534/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/rmt_hal.c.obj
[535/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/pcnt_hal.c.obj
[536/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sdm_hal.c.obj
[537/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal.c.obj
[538/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mcpwm_hal.c.obj
[539/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal_iram.c.obj
[540/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2s_hal.c.obj
[541/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sdmmc_hal.c.obj
[542/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal_common.c.obj
[543/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_oneshot_hal.c.obj
[544/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/brownout_hal.c.obj
[545/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/emac_hal.c.obj
[546/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sha_hal.c.obj
[547/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/aes_hal.c.obj
[548/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal.c.obj
[549/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpi_hal.c.obj
[550/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal_iram.c.obj
[551/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal.c.obj
[552/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal.c.obj
[553/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal_iram.c.obj
[554/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/touch_sensor_hal.c.obj
[555/1000] Linking C static library esp-idf\log\liblog.a
[556/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/touch_sensor_hal.c.obj
[557/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sdio_slave_hal.c.obj
[558/1000] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj
[559/1000] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj
[560/1000] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj
[561/1000] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/gpio_hal_workaround.c.obj
[562/1000] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj
[563/1000] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj
[564/1000] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj
[565/1000] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj
[566/1000] Linking C static library esp-idf\hal\libhal.a
[567/1000] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj
[568/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj
[569/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_ipc.c.obj
[570/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/crosscore_int.c.obj
[571/1000] Linking C static library esp-idf\esp_rom\libesp_rom.a
[572/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_system.c.obj
[573/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/freertos_hooks.c.obj
[574/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/panic.c.obj
[575/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/int_wdt.c.obj
[576/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup.c.obj
[577/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/stack_check.c.obj
[578/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/system_time.c.obj
[579/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup_funcs.c.obj
[580/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/ubsan.c.obj
[581/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/xt_wdt.c.obj
[582/1000] Building C object esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj
[583/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt.c.obj
[584/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt_impl_timergroup.c.obj
[585/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/panic_handler.c.obj
[586/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/image_process.c.obj
[587/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_system_chip.c.obj
[588/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/esp_ipc_isr_port.c.obj
[589/1000] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/esp_ipc_isr_routines.S.obj
[590/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/cpu_start.c.obj
[591/1000] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/esp_ipc_isr_handler.S.obj
[592/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/brownout.c.obj
[593/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_ipc_isr.c.obj
[594/1000] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/panic_handler_asm.S.obj
[595/1000] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/expression_with_stack_asm.S.obj
[596/1000] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/debug_helpers_asm.S.obj
[597/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/panic_arch.c.obj
[598/1000] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32/highint_hdl.S.obj
[599/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/debug_stubs.c.obj
[600/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/expression_with_stack.c.obj
[601/1000] Linking C static library esp-idf\esp_common\libesp_common.a
[602/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/trax.c.obj
[603/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32/reset_reason.c.obj
[604/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/debug_helpers.c.obj
[605/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_brownout_hook.c.obj
[606/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32/cache_err_int.c.obj
[607/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32/clk.c.obj
[608/1000] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32/system_internal.c.obj
[609/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_drivers.c.obj
[610/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_gd.c.obj
[611/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_issi.c.obj
[612/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_winbond.c.obj
[613/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_boya.c.obj
[614/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic.c.obj
[615/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_generic.c.obj
[616/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic_opi.c.obj
[617/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_th.c.obj
[618/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/memspi_host_driver.c.obj
[619/1000] Linking C static library esp-idf\esp_system\libesp_system.a
[620/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/cache_utils.c.obj
[621/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj
[622/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_ops.c.obj
[623/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_mmap.c.obj
[624/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_noos.c.obj
[625/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_spi_init.c.obj
[626/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_app.c.obj
[627/1000] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_api.c.obj
[628/1000] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/port/esp32/ext_mem_layout.c.obj
[629/1000] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/cache_esp32.c.obj
[630/1000] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_mmu_map.c.obj
[631/1000] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/heap_align_hw.c.obj
[632/1000] Linking C static library esp-idf\spi_flash\libspi_flash.a
[633/1000] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_cache.c.obj
[634/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj
[635/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj
[636/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj
[637/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj
[638/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj
[639/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj
[640/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj
[641/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj
[642/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj
[643/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj
[644/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj
[645/1000] Linking C static library esp-idf\esp_mm\libesp_mm.a
[646/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj
[647/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/idf/bootloader_sha.c.obj
[648/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/secure_boot_secure_features.c.obj
[649/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj
[650/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj
[651/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj
[652/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj
[653/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj
[654/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj
[655/1000] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj
[656/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj
[657/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj
[658/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj
[659/1000] Building C object esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition.c.obj
[660/1000] Linking C static library esp-idf\bootloader_support\libbootloader_support.a
[661/1000] Building C object esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj
[662/1000] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_startup.c.obj
[663/1000] Building C object esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition_target.c.obj
[664/1000] Building C object esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_app_desc.c.obj
[665/1000] Building C object esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_ops.c.obj
[666/1000] Building C object esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj
[667/1000] Linking C static library esp-idf\efuse\libefuse.a
[668/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_reader.c.obj
[669/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/debug.c.obj
[670/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_trace.c.obj
[671/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cache.c.obj
[672/1000] Linking C static library esp-idf\esp_partition\libesp_partition.a
[673/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_debug_helpers_generated.c.obj
[674/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ciphersuites.c.obj
[675/1000] Linking C static library esp-idf\app_update\libapp_update.a
[676/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cookie.c.obj
[677/1000] Linking C static library esp-idf\esp_bootloader_format\libesp_bootloader_format.a
[678/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_client.c.obj
[679/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ticket.c.obj
[680/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_keys.c.obj
[681/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_server.c.obj
[682/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_client.c.obj
[683/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_msg.c.obj
[684/1000] Linking C static library esp-idf\esp_app_format\libesp_app_format.a
[685/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_client.c.obj
[686/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls.c.obj
[687/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_server.c.obj
[688/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_generic.c.obj
[689/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/esp_platform_time.c.obj
[690/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/mbedtls_debug.c.obj
[691/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_create.c.obj
[692/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crl.c.obj
[693/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/pkcs7.c.obj
[694/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/net_sockets.c.obj
[695/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509.c.obj
[696/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write.c.obj
[697/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_csr.c.obj
[698/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_csr.c.obj
[699/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_crt.c.obj
[700/1000] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedtls.a
[701/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crt.c.obj
[702/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesni.c.obj
[703/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesce.c.obj
[704/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aes.c.obj
[705/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1parse.c.obj
[706/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aria.c.obj
[707/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1write.c.obj
[708/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod.c.obj
[709/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/base64.c.obj
[710/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_core.c.obj
[711/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod_raw.c.obj
[712/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/block_cipher.c.obj
[713/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/camellia.c.obj
[714/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chachapoly.c.obj
[715/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum.c.obj
[716/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chacha20.c.obj
[717/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ccm.c.obj
[718/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/constant_time.c.obj
[719/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher.c.obj
[720/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/des.c.obj
[721/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher_wrap.c.obj
[722/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ctr_drbg.c.obj
[723/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cmac.c.obj
[724/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/dhm.c.obj
[725/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdh.c.obj
[726/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdsa.c.obj
[727/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecjpake.c.obj
[728/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves_new.c.obj
[729/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy_poll.c.obj
[730/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hkdf.c.obj
[731/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy.c.obj
[732/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves.c.obj
[733/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/gcm.c.obj
[734/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp.c.obj
[735/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/error.c.obj
[736/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hmac_drbg.c.obj
[737/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lmots.c.obj
[738/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lms.c.obj
[739/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/nist_kw.c.obj
[740/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/memory_buffer_alloc.c.obj
[741/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md5.c.obj
[742/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/padlock.c.obj
[743/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pem.c.obj
[744/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md.c.obj
[745/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/oid.c.obj
[746/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk.c.obj
[747/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_ecc.c.obj
[748/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs12.c.obj
[749/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_wrap.c.obj
[750/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs5.c.obj
[751/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform.c.obj
[752/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform_util.c.obj
[753/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkparse.c.obj
[754/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/poly1305.c.obj
[755/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkwrite.c.obj
[756/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_aead.c.obj
[757/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_cipher.c.obj
[758/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_client.c.obj
[759/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_hash.c.obj
[760/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_driver_wrappers_no_static.c.obj
[761/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ffdh.c.obj
[762/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_mac.c.obj
[763/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ecp.c.obj
[764/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_pake.c.obj
[765/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_se.c.obj
[766/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_rsa.c.obj
[767/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_slot_management.c.obj
[768/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ripemd160.c.obj
[769/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_storage.c.obj
[770/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_its_file.c.obj
[771/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_util.c.obj
[772/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto.c.obj
[773/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa_alt_helpers.c.obj
[774/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha1.c.obj
[775/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/threading.c.obj
[776/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha256.c.obj
[777/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/timing.c.obj
[778/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa.c.obj
[779/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha512.c.obj
[780/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha3.c.obj
[781/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version.c.obj
[782/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/esp_hardware.c.obj
[783/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/esp_mem.c.obj
[784/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version_features.c.obj
[785/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/esp_timing.c.obj
[786/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/aes/esp_aes_xts.c.obj
[787/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/aes/esp_aes_common.c.obj
[788/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/sha/esp_sha.c.obj
[789/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/sha/parallel_engine/esp_sha1.c.obj
[790/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/sha/parallel_engine/sha.c.obj
[791/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/sha/parallel_engine/esp_sha256.c.obj
[792/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/bignum/bignum_alt.c.obj
[793/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/bignum/esp_bignum.c.obj
[794/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/aes/block/esp_aes.c.obj
[795/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/md/esp_md.c.obj
[796/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/sha/parallel_engine/esp_sha512.c.obj
[797/1000] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/aes/esp_aes_gcm.c.obj
[798/1000] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedx509.a
[799/1000] Creating directories for 'bootloader'
[800/1000] No download step for 'bootloader'
[801/1000] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/x25519.c.obj
[802/1000] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/everest.c.obj
[803/1000] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/Hacl_Curve25519_joined.c.obj
[804/1000] Building C object esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m/p256-m.c.obj
[805/1000] No update step for 'bootloader'
[806/1000] No patch step for 'bootloader'
[807/1000] Building C object esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m_driver_entrypoints.c.obj
[808/1000] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedcrypto.a
[809/1000] Linking CXX static library esp-idf\mbedtls\mbedtls\3rdparty\p256-m\libp256m.a
[810/1000] Linking CXX static library esp-idf\mbedtls\mbedtls\3rdparty\everest\libeverest.a
[811/1000] Generating x509_crt_bundle
[812/1000] Generating ../../x509_crt_bundle.S
[813/1000] Building ASM object esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/__/__/x509_crt_bundle.S.obj
[814/1000] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj
[815/1000] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_glitch_filter_ops.c.obj
[816/1000] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_trace.c.obj
[817/1000] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_locks.c.obj
[818/1000] Building ASM object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_context.S.obj
[819/1000] Building ASM object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_intr_asm.S.obj
[820/1000] Building C object esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/esp_crt_bundle/esp_crt_bundle.c.obj
[821/1000] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj
[822/1000] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/rtc_io.c.obj
[823/1000] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_impl.c.obj
[824/1000] Linking C static library esp-idf\mbedtls\libmbedtls.a
[825/1000] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_intr.c.obj
[826/1000] Building ASM object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_vectors.S.obj
[827/1000] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio.c.obj
[828/1000] Linking C static library esp-idf\esp_pm\libesp_pm.a
[829/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/split_argv.c.obj
[830/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/esp_console_common.c.obj
[831/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/commands.c.obj
[832/1000] Linking C static library esp-idf\esp_driver_gpio\libesp_driver_gpio.a
[833/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_date.c.obj
[834/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dbl.c.obj
[835/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/linenoise/linenoise.c.obj
[836/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_cmd.c.obj
[837/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dstr.c.obj
[838/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/esp_console_repl_chip.c.obj
[839/1000] Linking C static library esp-idf\xtensa\libxtensa.a
[840/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_end.c.obj
[841/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_file.c.obj
[842/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_hashtable.c.obj
[843/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rem.c.obj
[844/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_lit.c.obj
[845/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_int.c.obj
[846/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_str.c.obj
[847/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rex.c.obj
[848/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_utils.c.obj
[849/1000] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/argtable3.c.obj
[850/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_compat.c.obj
[851/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_runner.c.obj
[852/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_cache.c.obj
[853/1000] Building C object esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/protobuf-c/protobuf-c/protobuf-c.c.obj
[854/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity/src/unity.c.obj
[855/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_freertos.c.obj
[856/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_port_esp32.c.obj
[857/1000] Linking C static library esp-idf\console\libconsole.a
[858/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_memory.c.obj
[859/1000] Linking C static library esp-idf\protobuf-c\libprotobuf-c.a
[860/1000] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/port/esp/unity_utils_memory_esp.c.obj
[861/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/constants.pb-c.c.obj
[862/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/session.pb-c.c.obj
[863/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec0.pb-c.c.obj
[864/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec2.pb-c.c.obj
[865/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec1.pb-c.c.obj
[866/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/common/protocomm.c.obj
[867/1000] Building C object esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/src/https_server.c.obj
[868/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_console.c.obj
[869/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_httpd.c.obj
[870/1000] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/Partition.cpp.obj
[871/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security0.c.obj
[872/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp_mpi.c.obj
[873/1000] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/SPI_Flash.cpp.obj
[874/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp.c.obj
[875/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security2.c.obj
[876/1000] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Perf.cpp.obj
[877/1000] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security1.c.obj
[878/1000] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Safe.cpp.obj
[879/1000] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/crc32.cpp.obj
[880/1000] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/wear_levelling.cpp.obj
[881/1000] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Flash.cpp.obj
[882/1000] Building C object esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON_Utils.c.obj
[883/1000] Building C object esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON.c.obj
[884/1000] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_outbox.c.obj
[885/1000] Linking C static library esp-idf\unity\libunity.a
[886/1000] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/platform_esp32_idf.c.obj
[887/1000] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_msg.c.obj
[888/1000] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace_util.c.obj
[889/1000] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/host_file_io.c.obj
[890/1000] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace.c.obj
[891/1000] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/port/port_uart.c.obj
[892/1000] Building C object esp-idf/cmock/CMakeFiles/__idf_cmock.dir/CMock/src/cmock.c.obj
[893/1000] Building C object esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/dvp_share_ctrl.c.obj
[894/1000] Building C object esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/esp_cam_ctlr.c.obj
[895/1000] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/mqtt_client.c.obj
[896/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth_netif_glue.c.obj
[897/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth.c.obj
[898/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_dp83848.c.obj
[899/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/mac/esp_eth_mac_esp_dma.c.obj
[900/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/mac/esp_eth_mac_esp_gpio.c.obj
[901/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_802_3.c.obj
[902/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/mac/esp_eth_mac_esp.c.obj
[903/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_generic.c.obj
[904/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_ip101.c.obj
[905/1000] Linking C static library esp-idf\esp_https_server\libesp_https_server.a
[906/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_ksz80xx.c.obj
[907/1000] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hid_common.c.obj
[908/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_lan87xx.c.obj
[909/1000] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_rtl8201.c.obj
[910/1000] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidd.c.obj
[911/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_common.c.obj
[912/1000] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidh.c.obj
[913/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ssd1306.c.obj
[914/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_io.c.obj
[915/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ops.c.obj
[916/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_st7789.c.obj
[917/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_nt35510.c.obj
[918/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i2c/esp_lcd_panel_io_i2c_v1.c.obj
[919/1000] Linking C static library esp-idf\protocomm\libprotocomm.a
[920/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i2c/esp_lcd_panel_io_i2c_v2.c.obj
[921/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/spi/esp_lcd_panel_io_spi.c.obj
[922/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_init.c.obj
[923/1000] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/proto-c/esp_local_ctrl.pb-c.c.obj
[924/1000] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i80/esp_lcd_panel_io_i2s.c.obj
[925/1000] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl.c.obj
[926/1000] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_handler.c.obj
[927/1000] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_transport_httpd.c.obj
[928/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_sha.c.obj
[929/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_common.c.obj
[930/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_flash.c.obj
[931/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_uart.c.obj
[932/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_crc.c.obj
[933/1000] Linking C static library esp-idf\wear_levelling\libwear_levelling.a
[934/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_elf.c.obj
[935/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_binary.c.obj
[936/1000] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/port/xtensa/core_dump_port.c.obj
[937/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/port/freertos/ffsystem.c.obj
[938/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ffunicode.c.obj
[939/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio.c.obj
[940/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_rawflash.c.obj
[941/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_sdmmc.c.obj
[942/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_wl.c.obj
[943/1000] Linking C static library esp-idf\mqtt\libmqtt.a
[944/1000] Linking C static library esp-idf\json\libjson.a
[945/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ff.c.obj
[946/1000] Building C object esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/xtensa_perfmon_access.c.obj
[947/1000] Building C object esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/nvs_sec_provider.c.obj
[948/1000] Building C object esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/xtensa_perfmon_apis.c.obj
[949/1000] Building C object esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/xtensa_perfmon_masks.c.obj
[950/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat.c.obj
[951/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_sdmmc.c.obj
[952/1000] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_spiflash.c.obj
[953/1000] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_cache.c.obj
[954/1000] Building C object esp-idf/rt/CMakeFiles/__idf_rt.dir/FreeRTOS_POSIX_utils.c.obj
[955/1000] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_gc.c.obj
[956/1000] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs_api.c.obj
[957/1000] Building C object esp-idf/rt/CMakeFiles/__idf_rt.dir/FreeRTOS_POSIX_mqueue.c.obj
[958/1000] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_check.c.obj
[959/1000] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_hydrogen.c.obj
[960/1000] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_nucleus.c.obj
[961/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_config.c.obj
[962/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_ctrl.c.obj
[963/1000] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/esp_spiffs.c.obj
[964/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_config.pb-c.c.obj
[965/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_scan.c.obj
[966/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/handlers.c.obj
[967/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_console.c.obj
[968/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_scan.pb-c.c.obj
[969/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_ctrl.pb-c.c.obj
[970/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_constants.pb-c.c.obj
[971/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/manager.c.obj
[972/1000] Linking C static library esp-idf\app_trace\libapp_trace.a
[973/1000] Linking C static library esp-idf\cmock\libcmock.a
[974/1000] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_softap.c.obj
[975/1000] Linking C static library esp-idf\esp_driver_cam\libesp_driver_cam.a
[976/1000] Linking C static library esp-idf\esp_hid\libesp_hid.a
[977/1000] Linking C static library esp-idf\esp_eth\libesp_eth.a
[978/1000] Linking C static library esp-idf\esp_local_ctrl\libesp_local_ctrl.a
[979/1000] Linking C static library esp-idf\esp_lcd\libesp_lcd.a
[980/1000] Linking C static library esp-idf\espcoredump\libespcoredump.a
[981/1000] Linking C static library esp-idf\fatfs\libfatfs.a
[982/1000] Linking C static library esp-idf\nvs_sec_provider\libnvs_sec_provider.a
[983/1000] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/data_router.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/data_router.c.obj 
ccache C:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1-dirty\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/config -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/data_router.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\data_router.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/data_router.c.obj -c C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c: In function 'heartbeat_timer_callback':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:108:58: error: 'MACSTR' undeclared (first use in this function)
  108 |         snprintf(status.node_id, sizeof(status.node_id), MACSTR, MAC2STR(id.addr));
      |                                                          ^~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:108:58: note: each undeclared identifier is reported only once for each function it appears in
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:108:66: error: implicit declaration of function 'MAC2STR' [-Wimplicit-function-declaration]
  108 |         snprintf(status.node_id, sizeof(status.node_id), MACSTR, MAC2STR(id.addr));
      |                                                                  ^~~~~~~
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:4:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c: In function 'data_router_handle_mesh_data':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:46: error: expected ')' before 'MACSTR'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |                                              ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:60: note: to match this '('
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
In file included from C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:15:
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:46: error: expected ')' before 'MACSTR'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |                                              ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:60: note: to match this '('
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:46: error: expected ')' before 'MACSTR'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |                                              ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:60: note: to match this '('
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:46: error: expected ')' before 'MACSTR'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |                                              ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:60: note: to match this '('
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:46: error: expected ')' before 'MACSTR'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |                                              ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:60: note: to match this '('
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:163:5: note: in expansion of macro 'ESP_LOGI'
  163 |     ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
      |     ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:174:52: error: 'MACSTR' undeclared (first use in this function)
  174 |                 snprintf(node_id, sizeof(node_id), MACSTR, MAC2STR(from->addr));
      |                                                    ^~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:50: error: expected ')' before 'MACSTR'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |                                                  ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:60: note: to match this '('
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:50: error: expected ')' before 'MACSTR'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |                                                  ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:60: note: to match this '('
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:50: error: expected ')' before 'MACSTR'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |                                                  ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:60: note: to match this '('
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:50: error: expected ')' before 'MACSTR'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |                                                  ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:60: note: to match this '('
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:50: error: expected ')' before 'MACSTR'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |                                                  ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:60: note: to match this '('
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:210:9: note: in expansion of macro 'ESP_LOGI'
  210 |         ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c: In function 'data_router_send_sensor_data':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/data_router.c:296:44: error: 'MACSTR' undeclared (first use in this function)
  296 |         snprintf(node_id, sizeof(node_id), MACSTR, MAC2STR(id.addr));
      |                                            ^~~~~~
cc1.exe: some warnings being treated as errors
[984/1000] Linking C static library esp-idf\rt\librt.a
[985/1000] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
[986/1000] Linking C static library esp-idf\perfmon\libperfmon.a
[987/1000] Linking C static library esp-idf\spiffs\libspiffs.a
[988/1000] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj 
ccache C:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1-dirty\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/config -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\mesh_handler.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj -c C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:1:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:20:33: error: 'CONFIG_WIFI_SSID' undeclared here (not in a function)
   20 | #define WIFI_SSID               CONFIG_WIFI_SSID
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:18:17: note: in expansion of macro 'WIFI_SSID'
   18 |         .ssid = WIFI_SSID,
      |                 ^~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:21:33: error: 'CONFIG_WIFI_PASSWORD' undeclared here (not in a function); did you mean 'WIFI_PASSWORD'?
   21 | #define WIFI_PASSWORD           CONFIG_WIFI_PASSWORD
      |                                 ^~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:19:21: note: in expansion of macro 'WIFI_PASSWORD'
   19 |         .password = WIFI_PASSWORD,
      |                     ^~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:14:33: error: 'CONFIG_MESH_CHANNEL' undeclared here (not in a function)
   14 | #define MESH_CHANNEL            CONFIG_MESH_CHANNEL
      |                                 ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:25:16: note: in expansion of macro 'MESH_CHANNEL'
   25 |     .channel = MESH_CHANNEL,
      |                ^~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:12:33: error: 'CONFIG_MESH_ID' undeclared here (not in a function); did you mean 'CONFIG_TASK_WDT'?
   12 | #define MESH_ID                 CONFIG_MESH_ID
      |                                 ^~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:26:16: note: in expansion of macro 'MESH_ID'
   26 |     .mesh_id = MESH_ID,
      |                ^~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:13:33: error: 'CONFIG_MESH_PASSWORD' undeclared here (not in a function); did you mean 'MESH_PASSWORD'?
   13 | #define MESH_PASSWORD           CONFIG_MESH_PASSWORD
      |                                 ^~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:29:21: note: in expansion of macro 'MESH_PASSWORD'
   29 |         .password = MESH_PASSWORD,
      |                     ^~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:31:6: error: 'mesh_cfg_t' has no member named 'mesh_sta'
   31 |     .mesh_sta = {
      |      ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:31:5: warning: braces around scalar initializer
   31 |     .mesh_sta = {
      |     ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:31:5: note: (near initialization for 'mesh_cfg.crypto_funcs')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:32:9: error: field name not in record or union initializer
   32 |         .password = MESH_PASSWORD,
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:32:9: note: (near initialization for 'mesh_cfg.crypto_funcs')
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:4:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_event_handler':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:43: error: expected ')' before 'MACSTR'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |                                           ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:60: note: to match this '('
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
In file included from C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:15:
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:43: error: expected ')' before 'MACSTR'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |                                           ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:60: note: to match this '('
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:43: error: expected ')' before 'MACSTR'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |                                           ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:60: note: to match this '('
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:43: error: expected ')' before 'MACSTR'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |                                           ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:60: note: to match this '('
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:43: error: expected ')' before 'MACSTR'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |                                           ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:60: note: to match this '('
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:77:9: error: unknown type name 'mesh_layer_t'; did you mean 'mesh_addr_t'?
   77 |         mesh_layer_t layer = esp_mesh_get_layer();
      |         ^~~~~~~~~~~~
      |         mesh_addr_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:57: error: expected ')' before 'MACSTR'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |                                                         ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:60: note: to match this '('
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:57: error: expected ')' before 'MACSTR'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |                                                         ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:60: note: to match this '('
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:57: error: expected ')' before 'MACSTR'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |                                                         ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:60: note: to match this '('
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:57: error: expected ')' before 'MACSTR'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |                                                         ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:60: note: to match this '('
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:57: error: expected ')' before 'MACSTR'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |                                                         ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:60: note: to match this '('
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:78:9: note: in expansion of macro 'ESP_LOGI'
   78 |         ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:96:9: error: unknown type name 'mesh_layer_t'; did you mean 'mesh_addr_t'?
   96 |         mesh_layer_t new_layer = esp_mesh_get_layer();
      |         ^~~~~~~~~~~~
      |         mesh_addr_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_receive_task':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:49: error: expected ')' before 'MACSTR'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |                                                 ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:60: note: to match this '('
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:49: error: expected ')' before 'MACSTR'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |                                                 ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:60: note: to match this '('
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:49: error: expected ')' before 'MACSTR'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |                                                 ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:60: note: to match this '('
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:49: error: expected ')' before 'MACSTR'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |                                                 ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:60: note: to match this '('
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:49: error: expected ')' before 'MACSTR'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |                                                 ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:60: note: to match this '('
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%d' expects a matching 'int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:190:9: note: in expansion of macro 'ESP_LOGI'
  190 |         ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
      |         ^~~~~~~~
In file included from C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:73,
                 from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:4:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_init':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:15:33: error: 'CONFIG_MESH_MAX_LAYER' undeclared (first use in this function); did you mean 'MESH_MAX_LAYER'?
   15 | #define MESH_MAX_LAYER          CONFIG_MESH_MAX_LAYER
      |                                 ^~~~~~~~~~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:221:44: note: in expansion of macro 'MESH_MAX_LAYER'
  221 |     ESP_ERROR_CHECK(esp_mesh_set_max_layer(MESH_MAX_LAYER));
      |                                            ^~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:15:33: note: each undeclared identifier is reported only once for each function it appears in
   15 | #define MESH_MAX_LAYER          CONFIG_MESH_MAX_LAYER
      |                                 ^~~~~~~~~~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:221:44: note: in expansion of macro 'MESH_MAX_LAYER'
  221 |     ESP_ERROR_CHECK(esp_mesh_set_max_layer(MESH_MAX_LAYER));
      |                                            ^~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:233:41: error: passing argument 1 of 'esp_mesh_set_router' from incompatible pointer type [-Wincompatible-pointer-types]
  233 |     ESP_ERROR_CHECK(esp_mesh_set_router(&wifi_config));
      |                                         ^~~~~~~~~~~~
      |                                         |
      |                                         wifi_config_t *
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:825:52: note: expected 'const mesh_router_t *' but argument is of type 'wifi_config_t *'
  825 | esp_err_t esp_mesh_set_router(const mesh_router_t *router);
      |                               ~~~~~~~~~~~~~~~~~~~~~^~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_send_data':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:279:9: error: implicit declaration of function 'esp_mesh_get_root_addr'; did you mean 'esp_mesh_get_router'? [-Wimplicit-function-declaration]
  279 |         esp_mesh_get_root_addr(&root_addr);
      |         ^~~~~~~~~~~~~~~~~~~~~~
      |         esp_mesh_get_router
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_broadcast_data':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:319:47: error: passing argument 2 of 'esp_mesh_get_routing_table' makes integer from pointer without a cast [-Wint-conversion]
  319 |                                               &route_table_size, sizeof(mesh_addr_t)));
      |                                               ^~~~~~~~~~~~~~~~~
      |                                               |
      |                                               int *
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:1137:60: note: expected 'int' but argument is of type 'int *'
 1137 | esp_err_t esp_mesh_get_routing_table(mesh_addr_t *mac, int len, int *size);
      |                                                        ~~~~^~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:319:66: error: passing argument 3 of 'esp_mesh_get_routing_table' makes pointer from integer without a cast [-Wint-conversion]
  319 |                                               &route_table_size, sizeof(mesh_addr_t)));
      |                                                                  ^~~~~~~~~~~~~~~~~~~
      |                                                                  |
      |                                                                  unsigned int
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:1137:70: note: expected 'int *' but argument is of type 'unsigned int'
 1137 | esp_err_t esp_mesh_get_routing_table(mesh_addr_t *mac, int len, int *size);
      |                                                                 ~~~~~^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:53: error: expected ')' before 'MACSTR'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |                                                     ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:60: note: to match this '('
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:98:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   98 | #define LOG_COLOR_E           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_E'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:86: note: in expansion of macro 'LOG_FORMAT'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:53: error: expected ')' before 'MACSTR'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |                                                     ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:60: note: to match this '('
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:99:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
   99 | #define LOG_COLOR_W           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_W'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:86: note: in expansion of macro 'LOG_FORMAT'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:53: error: expected ')' before 'MACSTR'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |                                                     ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:60: note: to match this '('
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:101:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  101 | #define LOG_COLOR_D           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_D'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:86: note: in expansion of macro 'LOG_FORMAT'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:53: error: expected ')' before 'MACSTR'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |                                                     ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:60: note: to match this '('
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:102:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  102 | #define LOG_COLOR_V           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_V'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:86: note: in expansion of macro 'LOG_FORMAT'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:53: error: expected ')' before 'MACSTR'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |                                                     ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:88: note: in definition of macro 'LOG_FORMAT'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                                                                        ^~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:60: note: to match this '('
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                            ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%lu' expects a matching 'long unsigned int' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log_color.h:100:31: error: format '%s' expects a matching 'char *' argument [-Werror=format=]
  100 | #define LOG_COLOR_I           ""
      |                               ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:62:37: note: in expansion of macro 'LOG_COLOR_I'
   62 | #define LOG_FORMAT(letter, format)  LOG_COLOR_ ## letter #letter " (%" PRIu32 ") %s: " format LOG_RESET_COLOR "\n"
      |                                     ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:86: note: in expansion of macro 'LOG_FORMAT'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                      ^~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:205:38: note: in expansion of macro 'ESP_LOG_LEVEL'
  205 |         if (_ESP_LOG_ENABLED(level)) ESP_LOG_LEVEL(level, tag, format, ##__VA_ARGS__); \
      |                                      ^~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:113:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  113 | #define ESP_LOGW( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_WARN,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:325:13: note: in expansion of macro 'ESP_LOGW'
  325 |             ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_get_node_info':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:347:5: error: unknown type name 'mesh_layer_t'; did you mean 'mesh_addr_t'?
  347 |     mesh_layer_t layer = esp_mesh_get_layer();
      |     ^~~~~~~~~~~~
      |     mesh_addr_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:350:25: error: expected ')' before 'MACSTR'
  350 |              "Node ID: " MACSTR ", Layer: %d, Root: %s, Connected: %s",
      |                         ^~~~~~~
      |                         )
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:349:13: note: to match this '('
  349 |     snprintf(info_str, max_len,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:347:18: warning: unused variable 'layer' [-Wunused-variable]
  347 |     mesh_layer_t layer = esp_mesh_get_layer();
      |                  ^~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: At top level:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:13:22: warning: 'mesh_queue' defined but not used [-Wunused-variable]
   13 | static QueueHandle_t mesh_queue = NULL;
      |                      ^~~~~~~~~~
cc1.exe: some warnings being treated as errors
[989/1000] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj 
ccache C:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1-dirty\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/config -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\mqtt_client.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj -c C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:1:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:48:1: error: unknown type name 'bool'
   48 | bool mqtt_is_connected(void);
      | ^~~~
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:5:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:1:1: note: 'bool' is defined in header '<stdbool.h>'; this is probably fixable by adding '#include <stdbool.h>'
  +++ |+#include <stdbool.h>
    1 | #ifndef MQTT_CLIENT_H
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:12:8: error: unknown type name 'esp_mqtt_client_handle_t'
   12 | static esp_mqtt_client_handle_t mqtt_client = NULL;
      |        ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:12:47: error: initialization of 'int' from 'void *' makes integer from pointer without a cast [-Wint-conversion]
   12 | static esp_mqtt_client_handle_t mqtt_client = NULL;
      |                                               ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_event_handler':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:18:5: error: unknown type name 'esp_mqtt_event_handle_t'; did you mean 'esp_event_handler_t'?
   18 |     esp_mqtt_event_handle_t event = event_data;
      |     ^~~~~~~~~~~~~~~~~~~~~~~
      |     esp_event_handler_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:18:37: error: initialization of 'int' from 'void *' makes integer from pointer without a cast [-Wint-conversion]
   18 |     esp_mqtt_event_handle_t event = event_data;
      |                                     ^~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:19:5: error: unknown type name 'esp_mqtt_client_handle_t'
   19 |     esp_mqtt_client_handle_t client = event->client;
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:19:44: error: invalid type argument of '->' (have 'int')
   19 |     esp_mqtt_client_handle_t client = event->client;
      |                                            ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:14: error: 'esp_mqtt_event_id_t' undeclared (first use in this function); did you mean 'mesh_event_id_t'?
   21 |     switch ((esp_mqtt_event_id_t)event_id) {
      |              ^~~~~~~~~~~~~~~~~~~
      |              mesh_event_id_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:14: note: each undeclared identifier is reported only once for each function it appears in
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:34: error: expected ')' before 'event_id'
   21 |     switch ((esp_mqtt_event_id_t)event_id) {
      |            ~                     ^~~~~~~~
      |                                  )
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:22:10: error: 'MQTT_EVENT_CONNECTED' undeclared (first use in this function); did you mean 'WIFI_EVENT_STA_CONNECTED'?
   22 |     case MQTT_EVENT_CONNECTED:
      |          ^~~~~~~~~~~~~~~~~~~~
      |          WIFI_EVENT_STA_CONNECTED
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:27:9: error: implicit declaration of function 'esp_mqtt_client_subscribe' [-Wimplicit-function-declaration]
   27 |         esp_mqtt_client_subscribe(client, TB_RPC_REQUEST_TOPIC, 1);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:36:10: error: 'MQTT_EVENT_DISCONNECTED' undeclared (first use in this function); did you mean 'MESH_EVENT_CHILD_CONNECTED'?
   36 |     case MQTT_EVENT_DISCONNECTED:
      |          ^~~~~~~~~~~~~~~~~~~~~~~
      |          MESH_EVENT_CHILD_CONNECTED
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:41:10: error: 'MQTT_EVENT_SUBSCRIBED' undeclared (first use in this function)
   41 |     case MQTT_EVENT_SUBSCRIBED:
      |          ^~~~~~~~~~~~~~~~~~~~~
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:3:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:45:10: error: 'MQTT_EVENT_UNSUBSCRIBED' undeclared (first use in this function)
   45 |     case MQTT_EVENT_UNSUBSCRIBED:
      |          ^~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:49:10: error: 'MQTT_EVENT_PUBLISHED' undeclared (first use in this function)
   49 |     case MQTT_EVENT_PUBLISHED:
      |          ^~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:53:10: error: 'MQTT_EVENT_DATA' undeclared (first use in this function)
   53 |     case MQTT_EVENT_DATA:
      |          ^~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:59:43: error: invalid type argument of '->' (have 'int')
   59 |         data_router_handle_mqtt_data(event->topic, event->topic_len,
      |                                           ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:59:57: error: invalid type argument of '->' (have 'int')
   59 |         data_router_handle_mqtt_data(event->topic, event->topic_len,
      |                                                         ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:60:42: error: invalid type argument of '->' (have 'int')
   60 |                                     event->data, event->data_len);
      |                                          ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:60:55: error: invalid type argument of '->' (have 'int')
   60 |                                     event->data, event->data_len);
      |                                                       ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:63:10: error: 'MQTT_EVENT_ERROR' undeclared (first use in this function)
   63 |     case MQTT_EVENT_ERROR:
      |          ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:65:18: error: invalid type argument of '->' (have 'int')
   65 |         if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
      |                  ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:65:48: error: 'MQTT_ERROR_TYPE_TCP_TRANSPORT' undeclared (first use in this function)
   65 |         if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
      |                                                ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_init':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:88:5: error: unknown type name 'esp_mqtt_client_config_t'
   88 |     esp_mqtt_client_config_t mqtt_cfg = {
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: error: field name not in record or union initializer
   89 |         .broker = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:13: error: field name not in record or union initializer
   90 |             .address.hostname = THINGSBOARD_HOST,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:12:33: error: initialization of 'int' from 'char *' makes integer from pointer without a cast [-Wint-conversion]
   12 | #define THINGSBOARD_HOST        "mqtt://thingsboard.cloud"
      |                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:33: note: in expansion of macro 'THINGSBOARD_HOST'
   90 |             .address.hostname = THINGSBOARD_HOST,
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:12:33: note: (near initialization for 'mqtt_cfg')
   12 | #define THINGSBOARD_HOST        "mqtt://thingsboard.cloud"
      |                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:33: note: in expansion of macro 'THINGSBOARD_HOST'
   90 |             .address.hostname = THINGSBOARD_HOST,
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:13: error: field name not in record or union initializer
   91 |             .address.port = THINGSBOARD_PORT,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:13:33: warning: excess elements in scalar initializer
   13 | #define THINGSBOARD_PORT        1883
      |                                 ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:29: note: in expansion of macro 'THINGSBOARD_PORT'
   91 |             .address.port = THINGSBOARD_PORT,
      |                             ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:13:33: note: (near initialization for 'mqtt_cfg')
   13 | #define THINGSBOARD_PORT        1883
      |                                 ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:29: note: in expansion of macro 'THINGSBOARD_PORT'
   91 |             .address.port = THINGSBOARD_PORT,
      |                             ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: error: field name not in record or union initializer
   93 |         .credentials = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:13: error: field name not in record or union initializer
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:14:34: error: initialization of 'int' from 'char *' makes integer from pointer without a cast [-Wint-conversion]
   14 | #define THINGSBOARD_ACCESS_TOKEN "LgqL0Qn1v46tKJzKkZZf"
      |                                  ^~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:25: note: in expansion of macro 'THINGSBOARD_ACCESS_TOKEN'
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:14:34: note: (near initialization for 'mqtt_cfg')
   14 | #define THINGSBOARD_ACCESS_TOKEN "LgqL0Qn1v46tKJzKkZZf"
      |                                  ^~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:25: note: in expansion of macro 'THINGSBOARD_ACCESS_TOKEN'
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:13: error: field name not in record or union initializer
   95 |             .authentication.password = NULL,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:40: warning: excess elements in scalar initializer
   95 |             .authentication.password = NULL,
      |                                        ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:40: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:24: warning: excess elements in scalar initializer
   93 |         .credentials = {
      |                        ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:24: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: error: field name not in record or union initializer
   97 |         .session = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:98:13: error: field name not in record or union initializer
   98 |             .keepalive = 60,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:98:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:13: error: field name not in record or union initializer
   99 |             .disable_clean_session = false,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:38: warning: excess elements in scalar initializer
   99 |             .disable_clean_session = false,
      |                                      ^~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:38: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:20: warning: excess elements in scalar initializer
   97 |         .session = {
      |                    ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:20: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: error: field name not in record or union initializer
  101 |         .network = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:102:13: error: field name not in record or union initializer
  102 |             .disable_auto_reconnect = false,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:102:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:13: error: field name not in record or union initializer
  103 |             .timeout_ms = 5000,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:27: warning: excess elements in scalar initializer
  103 |             .timeout_ms = 5000,
      |                           ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:27: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:20: warning: excess elements in scalar initializer
  101 |         .network = {
      |                    ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:20: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: error: field name not in record or union initializer
  105 |         .task = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:106:13: error: field name not in record or union initializer
  106 |             .priority = 5,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:106:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:13: error: field name not in record or union initializer
  107 |             .stack_size = 6144,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:27: warning: excess elements in scalar initializer
  107 |             .stack_size = 6144,
      |                           ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:27: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:17: warning: excess elements in scalar initializer
  105 |         .task = {
      |                 ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:17: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:111:19: error: implicit declaration of function 'esp_mqtt_client_init'; did you mean 'mqtt_client_init'? [-Wimplicit-function-declaration]
  111 |     mqtt_client = esp_mqtt_client_init(&mqtt_cfg);
      |                   ^~~~~~~~~~~~~~~~~~~~
      |                   mqtt_client_init
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:117:5: error: implicit declaration of function 'esp_mqtt_client_register_event' [-Wimplicit-function-declaration]
  117 |     esp_mqtt_client_register_event(mqtt_client, ESP_EVENT_ANY_ID, mqtt_event_handler, NULL);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_start':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:130:12: error: implicit declaration of function 'esp_mqtt_client_start'; did you mean 'mqtt_client_start'? [-Wimplicit-function-declaration]
  130 |     return esp_mqtt_client_start(mqtt_client);
      |            ^~~~~~~~~~~~~~~~~~~~~
      |            mqtt_client_start
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_stop':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:138:16: error: implicit declaration of function 'esp_mqtt_client_stop'; did you mean 'mqtt_client_stop'? [-Wimplicit-function-declaration]
  138 |         return esp_mqtt_client_stop(mqtt_client);
      |                ^~~~~~~~~~~~~~~~~~~~
      |                mqtt_client_stop
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_publish_telemetry':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:150:18: error: implicit declaration of function 'esp_mqtt_client_publish' [-Wimplicit-function-declaration]
  150 |     int msg_id = esp_mqtt_client_publish(mqtt_client, TB_TELEMETRY_TOPIC,
      |                  ^~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: At top level:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:245:6: error: conflicting types for 'mqtt_is_connected'; have '_Bool(void)'
  245 | bool mqtt_is_connected(void)
      |      ^~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:48:6: note: previous declaration of 'mqtt_is_connected' with type 'int(void)'
   48 | bool mqtt_is_connected(void);
      |      ^~~~~~~~~~~~~~~~~
[990/1000] Linking C static library esp-idf\wifi_provisioning\libwifi_provisioning.a
[991/1000] Performing configure step for 'bootloader'
-- Found Git: C:/Espressif/tools/idf-git/2.44.0/cmd/git.exe (found version "2.44.0.windows.1")
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: C:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: C:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: C:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Building ESP-IDF components for target esp32
-- Project sdkconfig file C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/ld/esp32.peripherals.ld
-- Bootloader project name: "bootloader" version: 1
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.api.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.libgcc.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.newlib-funcs.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main/ld/esp32/bootloader.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main/ld/esp32/bootloader.rom.ld
-- Components: bootloader bootloader_support efuse esp_app_format esp_bootloader_format esp_common esp_hw_support esp_rom esp_security esp_system esptool_py freertos hal log main micro-ecc newlib partition_table soc spi_flash xtensa
-- Component paths: C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py C:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal C:/Espressif/frameworks/esp-idf-v5.4.1/components/log C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/components/micro-ecc C:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa
-- Configuring done (46.9s)
-- Generating done (0.6s)
-- Build files have been written to: C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader
ninja: build stopped: subcommand failed.
