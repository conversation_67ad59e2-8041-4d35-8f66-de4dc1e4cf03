[1/13] Performing build step for 'bootloader'
[1/1] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\kanagaraj\Sensor_project\AIOS_1\mesh_test\build\bootloader\esp-idf\esptool_py && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x1000 C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/bootloader.bin"

Bootloader binary size 0x6580 bytes. 0xa80 bytes (9%) free.


[2/13] No install step for 'bootloader'
[3/13] Completed 'bootloader'
[4/13] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
[5/13] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/data_router.c.obj
[6/13] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj 
ccache C:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1-dirty\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/config -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\mesh_handler.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj -c C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:1:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:12:33: warning: initializer-string for array of 'unsigned char' is too long
   12 | #define MESH_ID                 "mesh_thingsboard"
      |                                 ^~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:16: note: in expansion of macro 'MESH_ID'
   46 |     .mesh_id = MESH_ID,
      |                ^~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:12:33: note: (near initialization for 'mesh_cfg.mesh_id.addr')
   12 | #define MESH_ID                 "mesh_thingsboard"
      |                                 ^~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:46:16: note: in expansion of macro 'MESH_ID'
   46 |     .mesh_id = MESH_ID,
      |                ^~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:44:30: error: missing braces around initializer [-Werror=missing-braces]
   44 | static mesh_cfg_t mesh_cfg = {
      |                              ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:44:30: error: missing braces around initializer [-Werror=missing-braces]
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:30:22: warning: 'wifi_config' defined but not used [-Wunused-variable]
   30 | static wifi_config_t wifi_config = {
      |                      ^~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:27:22: warning: 'mesh_queue' defined but not used [-Wunused-variable]
   27 | static QueueHandle_t mesh_queue = NULL;
      |                      ^~~~~~~~~~
cc1.exe: some warnings being treated as errors
[7/13] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj 
ccache C:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1-dirty\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/config -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\mqtt_client.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj -c C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:1:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:48:1: error: unknown type name 'bool'
   48 | bool mqtt_is_connected(void);
      | ^~~~
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:5:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:1:1: note: 'bool' is defined in header '<stdbool.h>'; this is probably fixable by adding '#include <stdbool.h>'
  +++ |+#include <stdbool.h>
    1 | #ifndef MQTT_CLIENT_H
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:12:8: error: unknown type name 'esp_mqtt_client_handle_t'
   12 | static esp_mqtt_client_handle_t mqtt_client = NULL;
      |        ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:12:47: error: initialization of 'int' from 'void *' makes integer from pointer without a cast [-Wint-conversion]
   12 | static esp_mqtt_client_handle_t mqtt_client = NULL;
      |                                               ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_event_handler':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:18:5: error: unknown type name 'esp_mqtt_event_handle_t'; did you mean 'esp_event_handler_t'?
   18 |     esp_mqtt_event_handle_t event = event_data;
      |     ^~~~~~~~~~~~~~~~~~~~~~~
      |     esp_event_handler_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:18:37: error: initialization of 'int' from 'void *' makes integer from pointer without a cast [-Wint-conversion]
   18 |     esp_mqtt_event_handle_t event = event_data;
      |                                     ^~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:19:5: error: unknown type name 'esp_mqtt_client_handle_t'
   19 |     esp_mqtt_client_handle_t client = event->client;
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:19:44: error: invalid type argument of '->' (have 'int')
   19 |     esp_mqtt_client_handle_t client = event->client;
      |                                            ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:14: error: 'esp_mqtt_event_id_t' undeclared (first use in this function); did you mean 'mesh_event_id_t'?
   21 |     switch ((esp_mqtt_event_id_t)event_id) {
      |              ^~~~~~~~~~~~~~~~~~~
      |              mesh_event_id_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:14: note: each undeclared identifier is reported only once for each function it appears in
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:34: error: expected ')' before 'event_id'
   21 |     switch ((esp_mqtt_event_id_t)event_id) {
      |            ~                     ^~~~~~~~
      |                                  )
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:22:10: error: 'MQTT_EVENT_CONNECTED' undeclared (first use in this function); did you mean 'WIFI_EVENT_STA_CONNECTED'?
   22 |     case MQTT_EVENT_CONNECTED:
      |          ^~~~~~~~~~~~~~~~~~~~
      |          WIFI_EVENT_STA_CONNECTED
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:27:9: error: implicit declaration of function 'esp_mqtt_client_subscribe' [-Wimplicit-function-declaration]
   27 |         esp_mqtt_client_subscribe(client, TB_RPC_REQUEST_TOPIC, 1);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:36:10: error: 'MQTT_EVENT_DISCONNECTED' undeclared (first use in this function); did you mean 'MESH_EVENT_CHILD_CONNECTED'?
   36 |     case MQTT_EVENT_DISCONNECTED:
      |          ^~~~~~~~~~~~~~~~~~~~~~~
      |          MESH_EVENT_CHILD_CONNECTED
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:41:10: error: 'MQTT_EVENT_SUBSCRIBED' undeclared (first use in this function)
   41 |     case MQTT_EVENT_SUBSCRIBED:
      |          ^~~~~~~~~~~~~~~~~~~~~
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:3:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:45:10: error: 'MQTT_EVENT_UNSUBSCRIBED' undeclared (first use in this function)
   45 |     case MQTT_EVENT_UNSUBSCRIBED:
      |          ^~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:49:10: error: 'MQTT_EVENT_PUBLISHED' undeclared (first use in this function)
   49 |     case MQTT_EVENT_PUBLISHED:
      |          ^~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:53:10: error: 'MQTT_EVENT_DATA' undeclared (first use in this function)
   53 |     case MQTT_EVENT_DATA:
      |          ^~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:59:43: error: invalid type argument of '->' (have 'int')
   59 |         data_router_handle_mqtt_data(event->topic, event->topic_len,
      |                                           ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:59:57: error: invalid type argument of '->' (have 'int')
   59 |         data_router_handle_mqtt_data(event->topic, event->topic_len,
      |                                                         ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:60:42: error: invalid type argument of '->' (have 'int')
   60 |                                     event->data, event->data_len);
      |                                          ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:60:55: error: invalid type argument of '->' (have 'int')
   60 |                                     event->data, event->data_len);
      |                                                       ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:63:10: error: 'MQTT_EVENT_ERROR' undeclared (first use in this function)
   63 |     case MQTT_EVENT_ERROR:
      |          ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:65:18: error: invalid type argument of '->' (have 'int')
   65 |         if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
      |                  ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:65:48: error: 'MQTT_ERROR_TYPE_TCP_TRANSPORT' undeclared (first use in this function)
   65 |         if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
      |                                                ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_init':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:88:5: error: unknown type name 'esp_mqtt_client_config_t'
   88 |     esp_mqtt_client_config_t mqtt_cfg = {
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: error: field name not in record or union initializer
   89 |         .broker = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:13: error: field name not in record or union initializer
   90 |             .address.hostname = THINGSBOARD_HOST,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:12:33: error: initialization of 'int' from 'char *' makes integer from pointer without a cast [-Wint-conversion]
   12 | #define THINGSBOARD_HOST        "mqtt://thingsboard.cloud"
      |                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:33: note: in expansion of macro 'THINGSBOARD_HOST'
   90 |             .address.hostname = THINGSBOARD_HOST,
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:12:33: note: (near initialization for 'mqtt_cfg')
   12 | #define THINGSBOARD_HOST        "mqtt://thingsboard.cloud"
      |                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:33: note: in expansion of macro 'THINGSBOARD_HOST'
   90 |             .address.hostname = THINGSBOARD_HOST,
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:13: error: field name not in record or union initializer
   91 |             .address.port = THINGSBOARD_PORT,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:13:33: warning: excess elements in scalar initializer
   13 | #define THINGSBOARD_PORT        1883
      |                                 ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:29: note: in expansion of macro 'THINGSBOARD_PORT'
   91 |             .address.port = THINGSBOARD_PORT,
      |                             ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:13:33: note: (near initialization for 'mqtt_cfg')
   13 | #define THINGSBOARD_PORT        1883
      |                                 ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:29: note: in expansion of macro 'THINGSBOARD_PORT'
   91 |             .address.port = THINGSBOARD_PORT,
      |                             ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: error: field name not in record or union initializer
   93 |         .credentials = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:13: error: field name not in record or union initializer
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:14:34: error: initialization of 'int' from 'char *' makes integer from pointer without a cast [-Wint-conversion]
   14 | #define THINGSBOARD_ACCESS_TOKEN "LgqL0Qn1v46tKJzKkZZf"
      |                                  ^~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:25: note: in expansion of macro 'THINGSBOARD_ACCESS_TOKEN'
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:14:34: note: (near initialization for 'mqtt_cfg')
   14 | #define THINGSBOARD_ACCESS_TOKEN "LgqL0Qn1v46tKJzKkZZf"
      |                                  ^~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:25: note: in expansion of macro 'THINGSBOARD_ACCESS_TOKEN'
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:13: error: field name not in record or union initializer
   95 |             .authentication.password = NULL,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:40: warning: excess elements in scalar initializer
   95 |             .authentication.password = NULL,
      |                                        ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:40: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:24: warning: excess elements in scalar initializer
   93 |         .credentials = {
      |                        ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:24: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: error: field name not in record or union initializer
   97 |         .session = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:98:13: error: field name not in record or union initializer
   98 |             .keepalive = 60,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:98:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:13: error: field name not in record or union initializer
   99 |             .disable_clean_session = false,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:38: warning: excess elements in scalar initializer
   99 |             .disable_clean_session = false,
      |                                      ^~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:38: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:20: warning: excess elements in scalar initializer
   97 |         .session = {
      |                    ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:20: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: error: field name not in record or union initializer
  101 |         .network = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:102:13: error: field name not in record or union initializer
  102 |             .disable_auto_reconnect = false,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:102:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:13: error: field name not in record or union initializer
  103 |             .timeout_ms = 5000,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:27: warning: excess elements in scalar initializer
  103 |             .timeout_ms = 5000,
      |                           ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:27: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:20: warning: excess elements in scalar initializer
  101 |         .network = {
      |                    ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:20: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: error: field name not in record or union initializer
  105 |         .task = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:106:13: error: field name not in record or union initializer
  106 |             .priority = 5,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:106:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:13: error: field name not in record or union initializer
  107 |             .stack_size = 6144,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:27: warning: excess elements in scalar initializer
  107 |             .stack_size = 6144,
      |                           ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:27: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:17: warning: excess elements in scalar initializer
  105 |         .task = {
      |                 ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:17: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:111:19: error: implicit declaration of function 'esp_mqtt_client_init'; did you mean 'mqtt_client_init'? [-Wimplicit-function-declaration]
  111 |     mqtt_client = esp_mqtt_client_init(&mqtt_cfg);
      |                   ^~~~~~~~~~~~~~~~~~~~
      |                   mqtt_client_init
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:117:5: error: implicit declaration of function 'esp_mqtt_client_register_event' [-Wimplicit-function-declaration]
  117 |     esp_mqtt_client_register_event(mqtt_client, ESP_EVENT_ANY_ID, mqtt_event_handler, NULL);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_start':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:130:12: error: implicit declaration of function 'esp_mqtt_client_start'; did you mean 'mqtt_client_start'? [-Wimplicit-function-declaration]
  130 |     return esp_mqtt_client_start(mqtt_client);
      |            ^~~~~~~~~~~~~~~~~~~~~
      |            mqtt_client_start
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_stop':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:138:16: error: implicit declaration of function 'esp_mqtt_client_stop'; did you mean 'mqtt_client_stop'? [-Wimplicit-function-declaration]
  138 |         return esp_mqtt_client_stop(mqtt_client);
      |                ^~~~~~~~~~~~~~~~~~~~
      |                mqtt_client_stop
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_publish_telemetry':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:150:18: error: implicit declaration of function 'esp_mqtt_client_publish' [-Wimplicit-function-declaration]
  150 |     int msg_id = esp_mqtt_client_publish(mqtt_client, TB_TELEMETRY_TOPIC,
      |                  ^~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: At top level:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:245:6: error: conflicting types for 'mqtt_is_connected'; have '_Bool(void)'
  245 | bool mqtt_is_connected(void)
      |      ^~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:48:6: note: previous declaration of 'mqtt_is_connected' with type 'int(void)'
   48 | bool mqtt_is_connected(void);
      |      ^~~~~~~~~~~~~~~~~
ninja: build stopped: subcommand failed.
