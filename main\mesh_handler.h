#ifndef MESH_HANDLER_H
#define MESH_HANDLER_H

#include "esp_mesh.h"
#include "esp_wifi.h"

#ifdef __cplusplus
extern "C" {
#endif

// Mesh configuration
#define MESH_ID                 CONFIG_MESH_ID
#define MESH_PASSWORD           CONFIG_MESH_PASSWORD
#define MESH_CHANNEL            CONFIG_MESH_CHANNEL
#define MESH_MAX_LAYER          CONFIG_MESH_MAX_LAYER
#define MESH_AP_CONNECTIONS     6
#define MESH_NON_MESH_AP_CONNECTIONS 0

// WiFi configuration for root node
#define WIFI_SSID               CONFIG_WIFI_SSID
#define WIFI_PASSWORD           CONFIG_WIFI_PASSWORD

// Message types
typedef enum {
    MESH_MSG_SENSOR_DATA = 0x01,
    MESH_MSG_CLOUD_DATA = 0x02,
    MESH_MSG_COMMAND = 0x03,
    MESH_MSG_HEARTBEAT = 0x04
} mesh_msg_type_t;

// Mesh message structure
typedef struct {
    mesh_msg_type_t type;
    uint32_t timestamp;
    uint16_t data_len;
    uint8_t data[];
} __attribute__((packed)) mesh_message_t;

// Function declarations
esp_err_t mesh_init(void);
esp_err_t mesh_send_data(const uint8_t *data, size_t len, mesh_msg_type_t type);
esp_err_t mesh_broadcast_data(const uint8_t *data, size_t len, mesh_msg_type_t type);
bool mesh_is_root_node(void);
void mesh_get_node_info(char *info_str, size_t max_len);

#ifdef __cplusplus
}
#endif

#endif // MESH_HANDLER_H
