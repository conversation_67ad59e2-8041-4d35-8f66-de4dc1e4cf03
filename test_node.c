#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "esp_mesh.h"
#include "esp_netif.h"
#include "esp_random.h"
#include "cJSON.h"

static const char *TAG = "TEST_NODE";

// Mesh configuration - should match main project
#define MESH_ID                 "mesh_thingsboard"
#define MESH_PASSWORD           "mesh_password"
#define MESH_CHANNEL            6
#define MESH_MAX_LAYER          6
#define MESH_AP_CONNECTIONS     6

// WiFi configuration for root node (only used if this becomes root)
#define WIFI_SSID               "YOUR_WIFI_SSID"
#define WIFI_PASSWORD           "YOUR_WIFI_PASSWORD"

// Test message types
typedef enum {
    TEST_MSG_SENSOR_DATA = 0x01,
    TEST_MSG_PING = 0x02,
    TEST_MSG_STATUS = 0x03
} test_msg_type_t;

// Test message structure
typedef struct {
    test_msg_type_t type;
    uint32_t timestamp;
    uint16_t data_len;
    uint8_t data[];
} __attribute__((packed)) test_message_t;

// Test sensor data
typedef struct {
    float temperature;
    float humidity;
    int node_id;
    uint32_t sequence;
} test_sensor_data_t;

static bool is_mesh_connected = false;
static bool is_root = false;
static uint32_t message_sequence = 0;

// WiFi configuration for root node
static wifi_config_t wifi_config = {
    .sta = {
        .ssid = WIFI_SSID,
        .password = WIFI_PASSWORD,
    },
};

// Mesh configuration
static mesh_cfg_t mesh_cfg = {
    .channel = MESH_CHANNEL,
    .mesh_id = MESH_ID,
    .mesh_ap = {
        .max_connection = MESH_AP_CONNECTIONS,
        .password = MESH_PASSWORD,
    },
    .mesh_sta = {
        .password = MESH_PASSWORD,
    },
    .crypto_funcs = &g_wifi_default_mesh_crypto_funcs,
};

// Generate test sensor data
static void generate_test_data(test_sensor_data_t *data)
{
    static float temp_base = 20.0 + (esp_random() % 10); // 20-30°C base
    static float hum_base = 50.0 + (esp_random() % 30);  // 50-80% base
    
    // Add small variations
    temp_base += (esp_random() % 200 - 100) / 100.0; // ±1°C
    hum_base += (esp_random() % 100 - 50) / 10.0;    // ±5%
    
    // Keep in realistic ranges
    if (temp_base < 15.0) temp_base = 15.0;
    if (temp_base > 35.0) temp_base = 35.0;
    if (hum_base < 30.0) hum_base = 30.0;
    if (hum_base > 90.0) hum_base = 90.0;
    
    data->temperature = temp_base;
    data->humidity = hum_base;
    data->node_id = esp_random() % 1000; // Random node ID for testing
    data->sequence = ++message_sequence;
}

// Send test message via mesh
static esp_err_t send_test_message(const uint8_t *data, size_t len, test_msg_type_t type)
{
    if (!is_mesh_connected) {
        ESP_LOGW(TAG, "Mesh not connected, cannot send message");
        return ESP_ERR_MESH_NOT_START;
    }
    
    // Create test message
    size_t msg_size = sizeof(test_message_t) + len;
    test_message_t *msg = malloc(msg_size);
    if (!msg) {
        ESP_LOGE(TAG, "Failed to allocate memory for test message");
        return ESP_ERR_NO_MEM;
    }
    
    msg->type = type;
    msg->timestamp = esp_log_timestamp();
    msg->data_len = len;
    memcpy(msg->data, data, len);
    
    mesh_data_t mesh_data = {
        .data = (uint8_t *)msg,
        .size = msg_size,
        .proto = MESH_PROTO_BIN,
        .tos = MESH_TOS_P2P,
    };
    
    esp_err_t err;
    if (is_root) {
        ESP_LOGI(TAG, "Root node - message would be sent to cloud");
        err = ESP_OK;
    } else {
        // Send to root node
        mesh_addr_t root_addr;
        esp_mesh_get_root_addr(&root_addr);
        err = esp_mesh_send(&root_addr, &mesh_data, MESH_DATA_P2P, NULL, 0);
        
        if (err == ESP_OK) {
            ESP_LOGI(TAG, "Test message sent to root: type=%d, size=%d", type, msg_size);
        } else {
            ESP_LOGE(TAG, "Failed to send test message: %s", esp_err_to_name(err));
        }
    }
    
    free(msg);
    return err;
}

// Mesh event handler
static void mesh_event_handler(void *arg, esp_event_base_t event_base,
                              int32_t event_id, void *event_data)
{
    mesh_addr_t id = {0,};
    static uint16_t last_layer = 0;

    switch (event_id) {
    case MESH_EVENT_STARTED:
        esp_mesh_get_id(&id);
        ESP_LOGI(TAG, "TEST NODE - Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
        is_mesh_connected = esp_mesh_is_device_active();
        break;
        
    case MESH_EVENT_STOPPED:
        ESP_LOGI(TAG, "TEST NODE - Mesh stopped");
        is_mesh_connected = false;
        break;
        
    case MESH_EVENT_CHILD_CONNECTED:
        ESP_LOGI(TAG, "TEST NODE - Child connected");
        break;
        
    case MESH_EVENT_CHILD_DISCONNECTED:
        ESP_LOGI(TAG, "TEST NODE - Child disconnected");
        break;
        
    case MESH_EVENT_PARENT_CONNECTED:
        esp_mesh_get_id(&id);
        mesh_layer_t layer = esp_mesh_get_layer();
        ESP_LOGI(TAG, "TEST NODE - Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
        last_layer = layer;
        is_mesh_connected = true;
        
        if (esp_mesh_is_root()) {
            is_root = true;
            ESP_LOGI(TAG, "TEST NODE - Became root node");
        } else {
            is_root = false;
            ESP_LOGI(TAG, "TEST NODE - Connected as child node at layer %d", layer);
        }
        break;
        
    case MESH_EVENT_PARENT_DISCONNECTED:
        ESP_LOGI(TAG, "TEST NODE - Parent disconnected");
        is_mesh_connected = false;
        is_root = false;
        break;
        
    case MESH_EVENT_LAYER_CHANGE:
        mesh_layer_t new_layer = esp_mesh_get_layer();
        ESP_LOGI(TAG, "TEST NODE - Layer changed: %d -> %d", last_layer, new_layer);
        last_layer = new_layer;
        
        if (esp_mesh_is_root()) {
            is_root = true;
            ESP_LOGI(TAG, "TEST NODE - Became root node");
        } else {
            is_root = false;
            ESP_LOGI(TAG, "TEST NODE - Now at layer %d", new_layer);
        }
        break;
        
    default:
        ESP_LOGI(TAG, "TEST NODE - Mesh event: %ld", event_id);
        break;
    }
}

// Mesh receive task
static void mesh_receive_task(void *arg)
{
    mesh_addr_t from;
    mesh_data_t data;
    int flag = 0;
    esp_err_t err;
    
    ESP_LOGI(TAG, "TEST NODE - Starting mesh receive task");
    
    while (1) {
        data.data = malloc(MESH_MPS);
        data.size = MESH_MPS;
        
        err = esp_mesh_recv(&from, &data, portMAX_DELAY, &flag, NULL, 0);
        if (err != ESP_OK || !data.size) {
            ESP_LOGE(TAG, "TEST NODE - Mesh receive error: %s", esp_err_to_name(err));
            free(data.data);
            continue;
        }
        
        ESP_LOGI(TAG, "TEST NODE - Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));
        
        // Process received message
        if (data.size >= sizeof(test_message_t)) {
            test_message_t *msg = (test_message_t *)data.data;
            ESP_LOGI(TAG, "TEST NODE - Message type: %d, timestamp: %lu", msg->type, msg->timestamp);
            
            if (msg->type == TEST_MSG_SENSOR_DATA && msg->data_len >= sizeof(test_sensor_data_t)) {
                test_sensor_data_t *sensor_data = (test_sensor_data_t *)msg->data;
                ESP_LOGI(TAG, "TEST NODE - Sensor data: temp=%.1f, hum=%.1f, node=%d, seq=%lu",
                        sensor_data->temperature, sensor_data->humidity, 
                        sensor_data->node_id, sensor_data->sequence);
            }
        }
        
        free(data.data);
    }
}

// Test data generation task
static void test_data_task(void *arg)
{
    test_sensor_data_t sensor_data;
    
    ESP_LOGI(TAG, "TEST NODE - Starting test data generation");
    
    // Wait for mesh to be connected
    while (!is_mesh_connected) {
        ESP_LOGI(TAG, "TEST NODE - Waiting for mesh connection...");
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
    
    ESP_LOGI(TAG, "TEST NODE - Mesh connected, starting data transmission");
    
    while (1) {
        if (is_mesh_connected) {
            // Generate test sensor data
            generate_test_data(&sensor_data);
            
            ESP_LOGI(TAG, "TEST NODE - Generated data: temp=%.1f°C, hum=%.1f%%, node=%d, seq=%lu",
                    sensor_data.temperature, sensor_data.humidity, 
                    sensor_data.node_id, sensor_data.sequence);
            
            // Send sensor data
            esp_err_t err = send_test_message((uint8_t *)&sensor_data, sizeof(sensor_data), TEST_MSG_SENSOR_DATA);
            if (err != ESP_OK) {
                ESP_LOGE(TAG, "TEST NODE - Failed to send sensor data: %s", esp_err_to_name(err));
            }
            
            // Send ping message every 5th transmission
            if (sensor_data.sequence % 5 == 0) {
                char ping_msg[] = "PING from test node";
                send_test_message((uint8_t *)ping_msg, strlen(ping_msg), TEST_MSG_PING);
                ESP_LOGI(TAG, "TEST NODE - Sent ping message");
            }
        } else {
            ESP_LOGW(TAG, "TEST NODE - Mesh disconnected, waiting for reconnection...");
        }
        
        // Wait 15 seconds between transmissions
        vTaskDelay(pdMS_TO_TICKS(15000));
    }
}

// Status reporting task
static void status_task(void *arg)
{
    while (1) {
        mesh_addr_t id;
        esp_mesh_get_id(&id);
        mesh_layer_t layer = esp_mesh_get_layer();
        
        ESP_LOGI(TAG, "=== TEST NODE STATUS ===");
        ESP_LOGI(TAG, "Node ID: " MACSTR "", MAC2STR(id.addr));
        ESP_LOGI(TAG, "Layer: %d", layer);
        ESP_LOGI(TAG, "Is Root: %s", is_root ? "YES" : "NO");
        ESP_LOGI(TAG, "Connected: %s", is_mesh_connected ? "YES" : "NO");
        ESP_LOGI(TAG, "Free Heap: %lu bytes", esp_get_free_heap_size());
        ESP_LOGI(TAG, "Uptime: %lu ms", esp_log_timestamp());
        ESP_LOGI(TAG, "Messages Sent: %lu", message_sequence);
        ESP_LOGI(TAG, "========================");
        
        vTaskDelay(pdMS_TO_TICKS(30000)); // Status every 30 seconds
    }
}

// Initialize mesh network
static esp_err_t mesh_init(void)
{
    ESP_LOGI(TAG, "TEST NODE - Initializing mesh network");
    
    // Initialize WiFi
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    wifi_init_config_t config = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&config));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &mesh_event_handler, NULL));
    ESP_ERROR_CHECK(esp_wifi_set_storage(WIFI_STORAGE_FLASH));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    // Initialize mesh
    ESP_ERROR_CHECK(esp_mesh_init());
    ESP_ERROR_CHECK(esp_event_handler_register(MESH_EVENT, ESP_EVENT_ANY_ID, &mesh_event_handler, NULL));
    
    // Set mesh configuration
    ESP_ERROR_CHECK(esp_mesh_set_max_layer(MESH_MAX_LAYER));
    ESP_ERROR_CHECK(esp_mesh_set_vote_percentage(1));
    ESP_ERROR_CHECK(esp_mesh_set_xon_qsize(128));
    ESP_ERROR_CHECK(esp_mesh_allow_root_conflicts(false));
    ESP_ERROR_CHECK(esp_mesh_set_ap_assoc_expire(10));
    ESP_ERROR_CHECK(esp_mesh_set_announce_interval(600, 3300));
    
    // Set WiFi configuration
    ESP_ERROR_CHECK(esp_mesh_set_ap_authmode(WIFI_AUTH_WPA2_PSK));
    ESP_ERROR_CHECK(esp_mesh_set_config(&mesh_cfg));
    ESP_ERROR_CHECK(esp_mesh_set_router(&wifi_config));
    
    // Start mesh
    ESP_ERROR_CHECK(esp_mesh_start());
    
    ESP_LOGI(TAG, "TEST NODE - Mesh network initialized");
    return ESP_OK;
}

void app_main(void)
{
    ESP_LOGI(TAG, "=== ESP32 MESH TEST NODE STARTING ===");
    
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // Initialize mesh network
    mesh_init();
    
    // Create tasks
    xTaskCreate(mesh_receive_task, "mesh_receive", 4096, NULL, 5, NULL);
    xTaskCreate(test_data_task, "test_data", 4096, NULL, 4, NULL);
    xTaskCreate(status_task, "status", 3072, NULL, 3, NULL);
    
    ESP_LOGI(TAG, "=== TEST NODE INITIALIZED SUCCESSFULLY ===");
}
