#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_mac.h"
#include "nvs_flash.h"
#include "esp_mesh.h"
#include "esp_netif.h"

// MAC address formatting macros
#ifndef MACSTR
#define MACSTR "%02x:%02x:%02x:%02x:%02x:%02x"
#endif

#ifndef MAC2STR
#define MAC2STR(a) (a)[0], (a)[1], (a)[2], (a)[3], (a)[4], (a)[5]
#endif

static const char *TAG = "RECEIVER_NODE";

// Mesh configuration - should match main project
#define MESH_ID                 "mesh_thingsboard"
#define MESH_PASSWORD           "mesh_password"
#define MESH_CHANNEL            6
#define MESH_MAX_LAYER          6
#define MESH_AP_CONNECTIONS     6

// WiFi configuration (only used if this becomes root - which we don't want)
#define WIFI_SSID               "YOUR_WIFI_SSID"
#define WIFI_PASSWORD           "YOUR_WIFI_PASSWORD"

static bool is_mesh_connected = false;

// WiFi configuration (only used if becomes root)
static wifi_config_t wifi_config = {
    .sta = {
        .ssid = WIFI_SSID,
        .password = WIFI_PASSWORD,
    },
};

// Mesh configuration
static mesh_cfg_t mesh_cfg = {
    .channel = MESH_CHANNEL,
    .mesh_id = MESH_ID,
    .mesh_ap = {
        .max_connection = MESH_AP_CONNECTIONS,
        .password = MESH_PASSWORD,
    },
    .mesh_sta = {
        .password = MESH_PASSWORD,
    },
    .crypto_funcs = &g_wifi_default_mesh_crypto_funcs,
};

// Mesh event handler
static void mesh_event_handler(void *arg, esp_event_base_t event_base,
                              int32_t event_id, void *event_data)
{
    mesh_addr_t id = {0,};

    switch (event_id) {
    case MESH_EVENT_STARTED:
        esp_mesh_get_id(&id);
        ESP_LOGI(TAG, "RECEIVER - Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
        is_mesh_connected = esp_mesh_is_device_active();
        break;

    case MESH_EVENT_PARENT_CONNECTED:
        esp_mesh_get_id(&id);
        int layer = esp_mesh_get_layer();
        ESP_LOGI(TAG, "RECEIVER - Connected to mesh at layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
        is_mesh_connected = true;
        ESP_LOGI(TAG, "RECEIVER - Ready to receive data from root node");
        break;

    case MESH_EVENT_PARENT_DISCONNECTED:
        ESP_LOGI(TAG, "RECEIVER - Disconnected from mesh");
        is_mesh_connected = false;
        break;

    default:
        // Suppress other events for cleaner output
        break;
    }
}

// Mesh receive task - This is where we receive and print data from root
static void mesh_receive_task(void *arg)
{
    mesh_addr_t from;
    mesh_data_t data;
    int flag = 0;
    esp_err_t err;

    ESP_LOGI(TAG, "RECEIVER - Starting data receive task");

    while (1) {
        data.data = malloc(MESH_MPS);
        data.size = MESH_MPS;

        err = esp_mesh_recv(&from, &data, portMAX_DELAY, &flag, NULL, 0);
        if (err != ESP_OK || !data.size) {
            free(data.data);
            continue;
        }

        // Print received data information
        ESP_LOGI(TAG, "📨 RECEIVED DATA FROM ROOT:");
        ESP_LOGI(TAG, "   └─ From: " MACSTR "", MAC2STR(from.addr));
        ESP_LOGI(TAG, "   └─ Size: %d bytes", data.size);
        ESP_LOGI(TAG, "   └─ Timestamp: %lu ms", esp_log_timestamp());

        // Print raw data as hex
        ESP_LOGI(TAG, "   └─ Raw Data (hex):");
        for (int i = 0; i < data.size && i < 64; i += 16) {
            char hex_line[64] = {0};
            char ascii_line[17] = {0};

            for (int j = 0; j < 16 && (i + j) < data.size; j++) {
                sprintf(hex_line + j * 3, "%02X ", data.data[i + j]);
                ascii_line[j] = (data.data[i + j] >= 32 && data.data[i + j] <= 126) ? data.data[i + j] : '.';
            }
            ESP_LOGI(TAG, "      %04X: %-48s |%s|", i, hex_line, ascii_line);
        }

        // Try to interpret as string if printable
        bool is_printable = true;
        for (int i = 0; i < data.size; i++) {
            if (data.data[i] < 32 && data.data[i] != 0) {
                is_printable = false;
                break;
            }
        }

        if (is_printable && data.size > 0) {
            // Ensure null termination for safe printing
            char *str_data = malloc(data.size + 1);
            if (str_data) {
                memcpy(str_data, data.data, data.size);
                str_data[data.size] = '\0';
                ESP_LOGI(TAG, "   └─ As String: \"%s\"", str_data);
                free(str_data);
            }
        }

        ESP_LOGI(TAG, "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        free(data.data);
    }
}

// Simple status task
static void status_task(void *arg)
{
    while (1) {
        if (is_mesh_connected) {
            mesh_addr_t id;
            esp_mesh_get_id(&id);
            int layer = esp_mesh_get_layer();

            ESP_LOGI(TAG, "🔗 RECEIVER STATUS: Connected at Layer %d, ID: " MACSTR "",
                    layer, MAC2STR(id.addr));
            ESP_LOGI(TAG, "   └─ Waiting for data from root node...");
        } else {
            ESP_LOGI(TAG, "❌ RECEIVER STATUS: Not connected to mesh");
        }

        vTaskDelay(pdMS_TO_TICKS(30000)); // Status every 30 seconds
    }
}

// Initialize mesh network
static esp_err_t mesh_init(void)
{
    ESP_LOGI(TAG, "TEST NODE - Initializing mesh network");
    
    // Initialize WiFi
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    wifi_init_config_t config = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&config));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &mesh_event_handler, NULL));
    ESP_ERROR_CHECK(esp_wifi_set_storage(WIFI_STORAGE_FLASH));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    // Initialize mesh
    ESP_ERROR_CHECK(esp_mesh_init());
    ESP_ERROR_CHECK(esp_event_handler_register(MESH_EVENT, ESP_EVENT_ANY_ID, &mesh_event_handler, NULL));
    
    // Set mesh configuration
    ESP_ERROR_CHECK(esp_mesh_set_max_layer(MESH_MAX_LAYER));
    ESP_ERROR_CHECK(esp_mesh_set_vote_percentage(1));
    ESP_ERROR_CHECK(esp_mesh_set_xon_qsize(128));
    ESP_ERROR_CHECK(esp_mesh_allow_root_conflicts(false));
    ESP_ERROR_CHECK(esp_mesh_set_ap_assoc_expire(10));
    ESP_ERROR_CHECK(esp_mesh_set_announce_interval(600, 3300));
    
    // Set WiFi configuration
    ESP_ERROR_CHECK(esp_mesh_set_ap_authmode(WIFI_AUTH_WPA2_PSK));
    ESP_ERROR_CHECK(esp_mesh_set_config(&mesh_cfg));
    ESP_ERROR_CHECK(esp_mesh_set_router(&wifi_config));
    
    // Start mesh
    ESP_ERROR_CHECK(esp_mesh_start());
    
    ESP_LOGI(TAG, "TEST NODE - Mesh network initialized");
    return ESP_OK;
}

void app_main(void)
{
    ESP_LOGI(TAG, "🚀 ESP32 MESH RECEIVER NODE STARTING");
    ESP_LOGI(TAG, "   └─ Purpose: Receive and display data from root node");

    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Initialize mesh network
    mesh_init();

    // Create tasks - only receive and status
    xTaskCreate(mesh_receive_task, "mesh_receive", 4096, NULL, 5, NULL);
    xTaskCreate(status_task, "status", 3072, NULL, 3, NULL);

    ESP_LOGI(TAG, "✅ RECEIVER NODE READY - Listening for data...");
}
