{"version": "1.2", "project_name": "bootloader", "project_version": "v5.4.1-dirty", "project_path": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject", "idf_path": "C:/Espressif/frameworks/esp-idf-v5.4.1", "build_dir": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader", "config_file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig", "config_defaults": "", "bootloader_elf": "", "app_elf": "bootloader.elf", "app_bin": "bootloader.bin", "build_type": "flash_app", "git_revision": "v5.4.1-dirty", "target": "esp32", "rev": "0", "min_rev": "0", "max_rev": "399", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32-elf-", "c_compiler": "C:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/Kconfig;C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/Kconfig.projbuild"}, "common_component_reqs": ["log", "esp_rom", "esp_common", "esp_hw_support", "newlib", "xtensa"], "build_components": ["bootloader", "bootloader_support", "efuse", "esp_app_format", "esp_bootloader_format", "esp_common", "esp_hw_support", "esp_rom", "esp_security", "esp_system", "esptool_py", "freertos", "hal", "log", "main", "micro-ecc", "newlib", "partition_table", "soc", "spi_flash", "xtensa", ""], "build_component_paths": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/log", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/components/micro-ecc", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa", ""], "build_component_info": {"bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["micro-ecc", "spi_flash", "efuse", "esp_bootloader_format", "esp_app_format", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_common.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_common_loader.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_clock_init.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_mem.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_random.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_efuse.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/flash_encrypt.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/secure_boot.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_random_esp32.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_utility.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/flash_partitions.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/esp_image_format.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_init.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_clock_loader.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_console.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_console_loader.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/esp32/bootloader_sha.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/esp32/bootloader_soc.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/esp32/bootloader_esp32.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_panic.c"], "include_dirs": ["include", "bootloader_flash/include", "private_include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/efuse/libefuse.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/esp32/esp_efuse_table.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/esp32/esp_efuse_fields.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/esp32/esp_efuse_utility.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/esp_efuse_api.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/esp_efuse_fields.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/esp_efuse_utility.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c"], "include_dirs": ["include", "esp32/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format", "type": "CONFIG_ONLY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/esp_common/libesp_common.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/cpu.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/esp_cpu_intr.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/esp_memory_utils.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/cpu_region_protect.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/rtc_clk.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/rtc_clk_init.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/rtc_init.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/rtc_sleep.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/rtc_time.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/chip_info.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/esp_rom/libesp_rom.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_sys.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_print.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_crc.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_uart.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_spiflash.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_efuse.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_gpio.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_longjmp.S"], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security", "type": "CONFIG_ONLY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": ["spi_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/esp_system/libesp_system.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/esp_err.c"], "include_dirs": []}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos", "type": "CONFIG_ONLY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/hal/libhal.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/hal_utils.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/mpu_hal.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/efuse_hal.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/efuse_hal.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/wdt_hal_iram.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/mmu_hal.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/cache_hal_esp32.c"], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/log/liblog.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/noos/log_timestamp.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/log_timestamp_common.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/noos/log_lock.c"], "include_dirs": ["include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": ["bootloader", "bootloader_support"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/main/libmain.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main/bootloader_start.c"], "include_dirs": []}, "micro-ecc": {"alias": "idf::micro-ecc", "target": "___idf_micro-ecc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/components/micro-ecc", "type": "LIBRARY", "lib": "__idf_micro-ecc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/micro-ecc/libmicro-ecc.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c"], "include_dirs": [".", "micro-ecc"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib", "type": "CONFIG_ONLY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["platform_include"]}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/soc/libsoc.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/lldesc.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/dport_access_common.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/interrupts.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/gpio_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/uart_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/dport_access.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/adc_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/emac_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/spi_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/ledc_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/pcnt_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/rmt_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/sdm_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/i2s_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/i2c_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/timer_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/lcd_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/mcpwm_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/mpi_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/sdmmc_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/touch_sensor_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/twai_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/wdt_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/dac_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/rtc_io_periph.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/sdio_slave_periph.c"], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/spi_flash/libspi_flash.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_wrap.c"], "include_dirs": ["include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/esp-idf/xtensa/libxtensa.a", "sources": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/eri.c", "C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/xt_trax.c"], "include_dirs": ["esp32/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["micro-ecc", "spi_flash", "efuse", "esp_bootloader_format", "esp_app_format", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include", "private_include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/driver", "lib": "__idf_driver", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition", "lib": "__idf_esp_partition", "reqs": ["spi_flash"], "priv_reqs": ["bootloader_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm", "bootloader_support", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system", "lib": "__idf_esp_system", "reqs": ["spi_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element", "lib": "__idf_touch_element", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/main", "lib": "__idf_main", "reqs": ["bootloader", "bootloader_support"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "micro-ecc": {"alias": "idf::micro-ecc", "target": "___idf_micro-ecc", "prefix": "idf", "dir": "C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/subproject/components/micro-ecc", "lib": "__idf_micro-ecc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": [".", "micro-ecc"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/gdbinit/symbols", "02_prefix_map": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/gdbinit/prefix_map", "03_py_extensions": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/gdbinit/py_extensions", "04_connect": "C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32-wrover-kit-3.3v.cfg"}