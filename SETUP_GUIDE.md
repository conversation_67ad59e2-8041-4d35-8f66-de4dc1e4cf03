# ESP32 Mesh ThingsBoard Setup Guide

## Prerequisites

### Hardware Setup
1. **ESP32 Development Boards**: Minimum 2 boards, recommended 3-6 for testing
2. **USB Cables**: One for each ESP32 board
3. **WiFi Router**: With internet connection
4. **Computer**: With USB ports for programming

### Software Installation

#### 1. Install ESP-IDF
```bash
# For Linux/macOS
mkdir -p ~/esp
cd ~/esp
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
./install.sh
. ./export.sh

# For Windows (use ESP-IDF Command Prompt)
# Download and install ESP-IDF from Espressif website
```

#### 2. Verify Installation
```bash
idf.py --version
# Should show ESP-IDF version 4.4 or later
```

## ThingsBoard Account Setup

### 1. Create Account
- Go to [demo.thingsboard.io](https://demo.thingsboard.io)
- Sign up for a free account
- Login to the dashboard

### 2. Create Device
1. Navigate to **Devices** in the left menu
2. Click **"+"** button to add new device
3. Enter device details:
   - **Name**: ESP32_Mesh_Network
   - **Device Profile**: Default
   - **Label**: ESP32 Mesh IoT Device
4. Click **Add**
5. **Copy the Access Token** - you'll need this later

### 3. Create Dashboard (Optional)
1. Go to **Dashboards** → **"+"** → **Create new dashboard**
2. Name it "ESP32 Mesh Monitor"
3. Add widgets:
   - **Temperature Gauge**: Latest telemetry → temperature
   - **Humidity Gauge**: Latest telemetry → humidity
   - **Pressure Chart**: Time series → pressure
   - **Battery Level**: Latest telemetry → battery_level
   - **Node Status Table**: Latest telemetry → node_id

## Project Configuration

### 1. Clone Project
```bash
git clone <your-repository-url>
cd esp_mesh_thingsboard
```

### 2. Configure Project Settings
```bash
idf.py menuconfig
```

Navigate to **"ESP Mesh ThingsBoard Configuration"** and configure:

#### WiFi Settings (for Root Node)
- **WiFi SSID**: Your router's network name
- **WiFi Password**: Your router's password

#### ThingsBoard Settings
- **ThingsBoard Host**: demo.thingsboard.io (or your server)
- **ThingsBoard Port**: 1883
- **ThingsBoard Access Token**: Paste the token from step 2.5

#### Mesh Network Settings
- **Mesh Network ID**: mesh_thingsboard (or custom name)
- **Mesh Network Password**: mesh_password (or custom password)
- **Mesh WiFi Channel**: 6 (or available channel)
- **Maximum Mesh Layers**: 6

#### Data Settings
- **Dummy Data Interval**: 30 seconds
- **Heartbeat Interval**: 60 seconds

### 3. Save Configuration
- Press **S** to save
- Press **Q** to quit menuconfig

## Building and Flashing

### 1. Build Project
```bash
idf.py build
```

### 2. Connect ESP32 Boards
- Connect first ESP32 to USB port
- Check port name:
  ```bash
  # Linux/macOS
  ls /dev/ttyUSB* /dev/ttyACM*
  
  # Windows
  # Check Device Manager for COM ports
  ```

### 3. Flash First Device (Root Node)
```bash
# Replace /dev/ttyUSB0 with your actual port
idf.py -p /dev/ttyUSB0 flash

# Monitor output
idf.py -p /dev/ttyUSB0 monitor
```

### 4. Flash Additional Devices
```bash
# Connect second ESP32 to different USB port
idf.py -p /dev/ttyUSB1 flash

# Connect third ESP32 (if available)
idf.py -p /dev/ttyUSB2 flash

# Repeat for all devices
```

## Testing the System

### 1. Power On All Devices
- Connect all ESP32 boards to power
- Open serial monitors for debugging:
  ```bash
  # Terminal 1
  idf.py -p /dev/ttyUSB0 monitor
  
  # Terminal 2
  idf.py -p /dev/ttyUSB1 monitor
  
  # Terminal 3
  idf.py -p /dev/ttyUSB2 monitor
  ```

### 2. Verify Mesh Formation
Look for these messages in serial output:
```
I (xxxx) MESH_HANDLER: Mesh started, ID:xx:xx:xx:xx:xx:xx
I (xxxx) MESH_HANDLER: Parent connected, layer:1, ID:xx:xx:xx:xx:xx:xx
I (xxxx) MESH_HANDLER: Root node - starting MQTT client
I (xxxx) MQTT_CLIENT: MQTT Connected to ThingsBoard
```

### 3. Check ThingsBoard Dashboard
1. Go to your ThingsBoard dashboard
2. Navigate to **Devices** → **ESP32_Mesh_Network**
3. Click on **Latest Telemetry** tab
4. You should see incoming data:
   - temperature
   - humidity
   - pressure
   - light_level
   - battery_level
   - node_id

### 4. Test RPC Commands
1. In ThingsBoard, go to your device
2. Click **RPC** tab
3. Send test command:
   ```json
   {
     "method": "getValue",
     "params": {}
   }
   ```
4. Check response in the RPC tab

## Network Topology Verification

### Expected Behavior
1. **One device becomes root**: Connects to WiFi router and ThingsBoard
2. **Other devices join mesh**: Form parent-child relationships
3. **Data flows upward**: From leaf nodes → parent nodes → root → cloud
4. **Commands flow downward**: From cloud → root → all mesh nodes

### Troubleshooting Common Issues

#### Mesh Not Forming
```bash
# Check logs for:
E (xxxx) MESH_HANDLER: No parent found
E (xxxx) wifi: Association refused temporarily, comeback time 1000 (mSec)

# Solutions:
# 1. Ensure all devices have same MESH_ID and MESH_PASSWORD
# 2. Check devices are within WiFi range
# 3. Try different WiFi channel
# 4. Reset all devices simultaneously
```

#### Root Node Not Connecting to Internet
```bash
# Check logs for:
E (xxxx) wifi: wifi_sta_start: failed to start WiFi station
E (xxxx) MQTT_CLIENT: MQTT connection failed

# Solutions:
# 1. Verify WiFi SSID and password
# 2. Check router is broadcasting SSID
# 3. Ensure router has internet access
# 4. Try connecting ESP32 to router manually first
```

#### No Data in ThingsBoard
```bash
# Check logs for:
I (xxxx) MQTT_CLIENT: Published telemetry, msg_id=xxx
I (xxxx) DATA_ROUTER: Generated dummy data - Temp: xx.x°C

# Solutions:
# 1. Verify ThingsBoard access token
# 2. Check device is "Active" in ThingsBoard
# 3. Verify MQTT topics match ThingsBoard expectations
# 4. Check firewall/network restrictions
```

## Advanced Configuration

### Custom Sensor Integration
Replace dummy data in `main/data_router.c`:
```c
static void generate_real_sensor_data(sensor_data_t *data)
{
    // Replace with actual sensor readings
    data->temperature = dht22_read_temperature();
    data->humidity = dht22_read_humidity();
    data->pressure = bmp280_read_pressure();
    data->light_level = adc_read_light_sensor();
    data->battery_level = adc_read_battery_voltage();
    data->timestamp = esp_log_timestamp();
}
```

### Multiple Device Types
Modify `main/main.c` to support different device roles:
```c
void app_main(void)
{
    // ... existing initialization ...
    
    // Check GPIO pin to determine device role
    if (gpio_get_level(ROLE_SELECT_PIN)) {
        // Sensor node - generate data frequently
        data_router_set_mode(DATA_MODE_SENSOR);
    } else {
        // Relay node - focus on mesh routing
        data_router_set_mode(DATA_MODE_RELAY);
    }
}
```

### Custom MQTT Topics
Add custom topics in `main/mqtt_client.h`:
```c
#define CUSTOM_TELEMETRY_TOPIC  "v1/devices/me/telemetry/custom"
#define CUSTOM_COMMAND_TOPIC    "v1/devices/me/rpc/request/custom"
```

## Production Deployment

### Security Considerations
1. **Change default passwords**: Update mesh password and WiFi credentials
2. **Use TLS/SSL**: Enable secure MQTT connection
3. **Device certificates**: Use device-specific certificates
4. **OTA updates**: Implement secure over-the-air updates

### Performance Optimization
1. **Adjust task priorities**: Based on your application needs
2. **Memory management**: Monitor heap usage
3. **Power management**: Implement sleep modes for battery operation
4. **Mesh optimization**: Tune mesh parameters for your deployment

### Monitoring and Maintenance
1. **Log aggregation**: Collect logs from all devices
2. **Health monitoring**: Track device status and connectivity
3. **Automatic recovery**: Implement watchdog and recovery mechanisms
4. **Remote configuration**: Allow parameter updates via ThingsBoard

## Next Steps

1. **Test with real sensors**: Replace dummy data with actual sensor readings
2. **Implement actuators**: Add support for controlling devices via RPC
3. **Add data persistence**: Store data locally during connectivity issues
4. **Scale the network**: Test with more devices and larger mesh networks
5. **Custom dashboard**: Create application-specific ThingsBoard dashboards

For additional help, refer to:
- [ESP-IDF Programming Guide](https://docs.espressif.com/projects/esp-idf/en/latest/)
- [ThingsBoard Documentation](https://thingsboard.io/docs/)
- [ESP32 Mesh Development Framework](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-guides/mesh.html)
