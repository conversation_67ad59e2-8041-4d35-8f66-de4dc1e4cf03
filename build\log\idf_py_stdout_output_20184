[1/12] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj 
ccache C:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1-dirty\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/config -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\mesh_handler.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj -c C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:1:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:20:33: error: 'CONFIG_WIFI_SSID' undeclared here (not in a function)
   20 | #define WIFI_SSID               CONFIG_WIFI_SSID
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:28:17: note: in expansion of macro 'WIFI_SSID'
   28 |         .ssid = WIFI_SSID,
      |                 ^~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:21:33: error: 'CONFIG_WIFI_PASSWORD' undeclared here (not in a function); did you mean 'WIFI_PASSWORD'?
   21 | #define WIFI_PASSWORD           CONFIG_WIFI_PASSWORD
      |                                 ^~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:29:21: note: in expansion of macro 'WIFI_PASSWORD'
   29 |         .password = WIFI_PASSWORD,
      |                     ^~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:14:33: error: 'CONFIG_MESH_CHANNEL' undeclared here (not in a function)
   14 | #define MESH_CHANNEL            CONFIG_MESH_CHANNEL
      |                                 ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:35:16: note: in expansion of macro 'MESH_CHANNEL'
   35 |     .channel = MESH_CHANNEL,
      |                ^~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:12:33: error: 'CONFIG_MESH_ID' undeclared here (not in a function); did you mean 'CONFIG_TASK_WDT'?
   12 | #define MESH_ID                 CONFIG_MESH_ID
      |                                 ^~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:36:16: note: in expansion of macro 'MESH_ID'
   36 |     .mesh_id = MESH_ID,
      |                ^~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:13:33: error: 'CONFIG_MESH_PASSWORD' undeclared here (not in a function); did you mean 'MESH_PASSWORD'?
   13 | #define MESH_PASSWORD           CONFIG_MESH_PASSWORD
      |                                 ^~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:39:21: note: in expansion of macro 'MESH_PASSWORD'
   39 |         .password = MESH_PASSWORD,
      |                     ^~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:41:6: error: 'mesh_cfg_t' has no member named 'mesh_sta'
   41 |     .mesh_sta = {
      |      ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:41:5: warning: braces around scalar initializer
   41 |     .mesh_sta = {
      |     ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:41:5: note: (near initialization for 'mesh_cfg.crypto_funcs')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:42:9: error: field name not in record or union initializer
   42 |         .password = MESH_PASSWORD,
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:42:9: note: (near initialization for 'mesh_cfg.crypto_funcs')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_event_handler':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:87:9: error: unknown type name 'mesh_layer_t'; did you mean 'mesh_addr_t'?
   87 |         mesh_layer_t layer = esp_mesh_get_layer();
      |         ^~~~~~~~~~~~
      |         mesh_addr_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:106:9: error: unknown type name 'mesh_layer_t'; did you mean 'mesh_addr_t'?
  106 |         mesh_layer_t new_layer = esp_mesh_get_layer();
      |         ^~~~~~~~~~~~
      |         mesh_addr_t
In file included from C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:73,
                 from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:4:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_init':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:15:33: error: 'CONFIG_MESH_MAX_LAYER' undeclared (first use in this function); did you mean 'MESH_MAX_LAYER'?
   15 | #define MESH_MAX_LAYER          CONFIG_MESH_MAX_LAYER
      |                                 ^~~~~~~~~~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:231:44: note: in expansion of macro 'MESH_MAX_LAYER'
  231 |     ESP_ERROR_CHECK(esp_mesh_set_max_layer(MESH_MAX_LAYER));
      |                                            ^~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.h:15:33: note: each undeclared identifier is reported only once for each function it appears in
   15 | #define MESH_MAX_LAYER          CONFIG_MESH_MAX_LAYER
      |                                 ^~~~~~~~~~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:231:44: note: in expansion of macro 'MESH_MAX_LAYER'
  231 |     ESP_ERROR_CHECK(esp_mesh_set_max_layer(MESH_MAX_LAYER));
      |                                            ^~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:243:41: error: passing argument 1 of 'esp_mesh_set_router' from incompatible pointer type [-Wincompatible-pointer-types]
  243 |     ESP_ERROR_CHECK(esp_mesh_set_router(&wifi_config));
      |                                         ^~~~~~~~~~~~
      |                                         |
      |                                         wifi_config_t *
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:825:52: note: expected 'const mesh_router_t *' but argument is of type 'wifi_config_t *'
  825 | esp_err_t esp_mesh_set_router(const mesh_router_t *router);
      |                               ~~~~~~~~~~~~~~~~~~~~~^~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_send_data':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:289:9: error: implicit declaration of function 'esp_mesh_get_root_addr'; did you mean 'esp_mesh_get_router'? [-Wimplicit-function-declaration]
  289 |         esp_mesh_get_root_addr(&root_addr);
      |         ^~~~~~~~~~~~~~~~~~~~~~
      |         esp_mesh_get_router
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_broadcast_data':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:329:47: error: passing argument 2 of 'esp_mesh_get_routing_table' makes integer from pointer without a cast [-Wint-conversion]
  329 |                                               &route_table_size, sizeof(mesh_addr_t)));
      |                                               ^~~~~~~~~~~~~~~~~
      |                                               |
      |                                               int *
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:1137:60: note: expected 'int' but argument is of type 'int *'
 1137 | esp_err_t esp_mesh_get_routing_table(mesh_addr_t *mac, int len, int *size);
      |                                                        ~~~~^~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:329:66: error: passing argument 3 of 'esp_mesh_get_routing_table' makes pointer from integer without a cast [-Wint-conversion]
  329 |                                               &route_table_size, sizeof(mesh_addr_t)));
      |                                                                  ^~~~~~~~~~~~~~~~~~~
      |                                                                  |
      |                                                                  unsigned int
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include/esp_err.h:116:30: note: in definition of macro 'ESP_ERROR_CHECK'
  116 |         esp_err_t err_rc_ = (x);                                        \
      |                              ^
C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/esp_mesh.h:1137:70: note: expected 'int *' but argument is of type 'unsigned int'
 1137 | esp_err_t esp_mesh_get_routing_table(mesh_addr_t *mac, int len, int *size);
      |                                                                 ~~~~~^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: In function 'mesh_get_node_info':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:357:5: error: unknown type name 'mesh_layer_t'; did you mean 'mesh_addr_t'?
  357 |     mesh_layer_t layer = esp_mesh_get_layer();
      |     ^~~~~~~~~~~~
      |     mesh_addr_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c: At top level:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mesh_handler.c:23:22: warning: 'mesh_queue' defined but not used [-Wunused-variable]
   23 | static QueueHandle_t mesh_queue = NULL;
      |                      ^~~~~~~~~~
[2/12] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/data_router.c.obj
[3/12] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj 
ccache C:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1-dirty\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/config -IC:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/config/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.1=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\mqtt_client.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/mqtt_client.c.obj -c C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:1:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:48:1: error: unknown type name 'bool'
   48 | bool mqtt_is_connected(void);
      | ^~~~
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:5:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:1:1: note: 'bool' is defined in header '<stdbool.h>'; this is probably fixable by adding '#include <stdbool.h>'
  +++ |+#include <stdbool.h>
    1 | #ifndef MQTT_CLIENT_H
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:12:8: error: unknown type name 'esp_mqtt_client_handle_t'
   12 | static esp_mqtt_client_handle_t mqtt_client = NULL;
      |        ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:12:47: error: initialization of 'int' from 'void *' makes integer from pointer without a cast [-Wint-conversion]
   12 | static esp_mqtt_client_handle_t mqtt_client = NULL;
      |                                               ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_event_handler':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:18:5: error: unknown type name 'esp_mqtt_event_handle_t'; did you mean 'esp_event_handler_t'?
   18 |     esp_mqtt_event_handle_t event = event_data;
      |     ^~~~~~~~~~~~~~~~~~~~~~~
      |     esp_event_handler_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:18:37: error: initialization of 'int' from 'void *' makes integer from pointer without a cast [-Wint-conversion]
   18 |     esp_mqtt_event_handle_t event = event_data;
      |                                     ^~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:19:5: error: unknown type name 'esp_mqtt_client_handle_t'
   19 |     esp_mqtt_client_handle_t client = event->client;
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:19:44: error: invalid type argument of '->' (have 'int')
   19 |     esp_mqtt_client_handle_t client = event->client;
      |                                            ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:14: error: 'esp_mqtt_event_id_t' undeclared (first use in this function); did you mean 'mesh_event_id_t'?
   21 |     switch ((esp_mqtt_event_id_t)event_id) {
      |              ^~~~~~~~~~~~~~~~~~~
      |              mesh_event_id_t
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:14: note: each undeclared identifier is reported only once for each function it appears in
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:21:34: error: expected ')' before 'event_id'
   21 |     switch ((esp_mqtt_event_id_t)event_id) {
      |            ~                     ^~~~~~~~
      |                                  )
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:22:10: error: 'MQTT_EVENT_CONNECTED' undeclared (first use in this function); did you mean 'WIFI_EVENT_STA_CONNECTED'?
   22 |     case MQTT_EVENT_CONNECTED:
      |          ^~~~~~~~~~~~~~~~~~~~
      |          WIFI_EVENT_STA_CONNECTED
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:27:9: error: implicit declaration of function 'esp_mqtt_client_subscribe' [-Wimplicit-function-declaration]
   27 |         esp_mqtt_client_subscribe(client, TB_RPC_REQUEST_TOPIC, 1);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:36:10: error: 'MQTT_EVENT_DISCONNECTED' undeclared (first use in this function); did you mean 'MESH_EVENT_CHILD_CONNECTED'?
   36 |     case MQTT_EVENT_DISCONNECTED:
      |          ^~~~~~~~~~~~~~~~~~~~~~~
      |          MESH_EVENT_CHILD_CONNECTED
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:41:10: error: 'MQTT_EVENT_SUBSCRIBED' undeclared (first use in this function)
   41 |     case MQTT_EVENT_SUBSCRIBED:
      |          ^~~~~~~~~~~~~~~~~~~~~
In file included from C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:3:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:58: error: invalid type argument of '->' (have 'int')
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |                                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:42:9: note: in expansion of macro 'ESP_LOGI'
   42 |         ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:45:10: error: 'MQTT_EVENT_UNSUBSCRIBED' undeclared (first use in this function)
   45 |     case MQTT_EVENT_UNSUBSCRIBED:
      |          ^~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:60: error: invalid type argument of '->' (have 'int')
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |                                                            ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:46:9: note: in expansion of macro 'ESP_LOGI'
   46 |         ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:49:10: error: 'MQTT_EVENT_PUBLISHED' undeclared (first use in this function)
   49 |     case MQTT_EVENT_PUBLISHED:
      |          ^~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:57: error: invalid type argument of '->' (have 'int')
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |                                                         ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:50:9: note: in expansion of macro 'ESP_LOGI'
   50 |         ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:53:10: error: 'MQTT_EVENT_DATA' undeclared (first use in this function)
   53 |     case MQTT_EVENT_DATA:
      |          ^~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:43: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:61: error: invalid type argument of '->' (have 'int')
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |                                                             ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:55:9: note: in expansion of macro 'ESP_LOGI'
   55 |         ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:42: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                          ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:59: error: invalid type argument of '->' (have 'int')
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |                                                           ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:56:9: note: in expansion of macro 'ESP_LOGI'
   56 |         ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:59:43: error: invalid type argument of '->' (have 'int')
   59 |         data_router_handle_mqtt_data(event->topic, event->topic_len,
      |                                           ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:59:57: error: invalid type argument of '->' (have 'int')
   59 |         data_router_handle_mqtt_data(event->topic, event->topic_len,
      |                                                         ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:60:42: error: invalid type argument of '->' (have 'int')
   60 |                                     event->data, event->data_len);
      |                                          ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:60:55: error: invalid type argument of '->' (have 'int')
   60 |                                     event->data, event->data_len);
      |                                                       ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:63:10: error: 'MQTT_EVENT_ERROR' undeclared (first use in this function)
   63 |     case MQTT_EVENT_ERROR:
      |          ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:65:18: error: invalid type argument of '->' (have 'int')
   65 |         if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
      |                  ^~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:65:48: error: 'MQTT_ERROR_TYPE_TCP_TRANSPORT' undeclared (first use in this function)
   65 |         if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
      |                                                ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:67: error: invalid type argument of '->' (have 'int')
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |                                                                   ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:66:13: note: in expansion of macro 'ESP_LOGI'
   66 |             ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
      |             ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:182:137: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:183:137: note: in definition of macro 'ESP_LOG_LEVEL'
  183 |         else if (level==ESP_LOG_WARN )      { esp_log_write(ESP_LOG_WARN,       tag, LOG_FORMAT(W, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:184:137: note: in definition of macro 'ESP_LOG_LEVEL'
  184 |         else if (level==ESP_LOG_DEBUG )     { esp_log_write(ESP_LOG_DEBUG,      tag, LOG_FORMAT(D, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:185:137: note: in definition of macro 'ESP_LOG_LEVEL'
  185 |         else if (level==ESP_LOG_VERBOSE )   { esp_log_write(ESP_LOG_VERBOSE,    tag, LOG_FORMAT(V, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:54: error: invalid type argument of '->' (have 'int')
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |                                                      ^~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:186:137: note: in definition of macro 'ESP_LOG_LEVEL'
  186 |         else                                { esp_log_write(ESP_LOG_INFO,       tag, LOG_FORMAT(I, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                                                                         ^~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.4.1/components/log/include/esp_log.h:114:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  114 | #define ESP_LOGI( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_INFO,    tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:71:9: note: in expansion of macro 'ESP_LOGI'
   71 |         ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
      |         ^~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_init':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:88:5: error: unknown type name 'esp_mqtt_client_config_t'
   88 |     esp_mqtt_client_config_t mqtt_cfg = {
      |     ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: error: field name not in record or union initializer
   89 |         .broker = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:89:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:13: error: field name not in record or union initializer
   90 |             .address.hostname = THINGSBOARD_HOST,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:12:33: error: initialization of 'int' from 'char *' makes integer from pointer without a cast [-Wint-conversion]
   12 | #define THINGSBOARD_HOST        "mqtt://thingsboard.cloud"
      |                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:33: note: in expansion of macro 'THINGSBOARD_HOST'
   90 |             .address.hostname = THINGSBOARD_HOST,
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:12:33: note: (near initialization for 'mqtt_cfg')
   12 | #define THINGSBOARD_HOST        "mqtt://thingsboard.cloud"
      |                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:90:33: note: in expansion of macro 'THINGSBOARD_HOST'
   90 |             .address.hostname = THINGSBOARD_HOST,
      |                                 ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:13: error: field name not in record or union initializer
   91 |             .address.port = THINGSBOARD_PORT,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:13:33: warning: excess elements in scalar initializer
   13 | #define THINGSBOARD_PORT        1883
      |                                 ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:29: note: in expansion of macro 'THINGSBOARD_PORT'
   91 |             .address.port = THINGSBOARD_PORT,
      |                             ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:13:33: note: (near initialization for 'mqtt_cfg')
   13 | #define THINGSBOARD_PORT        1883
      |                                 ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:91:29: note: in expansion of macro 'THINGSBOARD_PORT'
   91 |             .address.port = THINGSBOARD_PORT,
      |                             ^~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: error: field name not in record or union initializer
   93 |         .credentials = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:13: error: field name not in record or union initializer
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:14:34: error: initialization of 'int' from 'char *' makes integer from pointer without a cast [-Wint-conversion]
   14 | #define THINGSBOARD_ACCESS_TOKEN "LgqL0Qn1v46tKJzKkZZf"
      |                                  ^~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:25: note: in expansion of macro 'THINGSBOARD_ACCESS_TOKEN'
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:14:34: note: (near initialization for 'mqtt_cfg')
   14 | #define THINGSBOARD_ACCESS_TOKEN "LgqL0Qn1v46tKJzKkZZf"
      |                                  ^~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:94:25: note: in expansion of macro 'THINGSBOARD_ACCESS_TOKEN'
   94 |             .username = THINGSBOARD_ACCESS_TOKEN,
      |                         ^~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:13: error: field name not in record or union initializer
   95 |             .authentication.password = NULL,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:40: warning: excess elements in scalar initializer
   95 |             .authentication.password = NULL,
      |                                        ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:95:40: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:24: warning: excess elements in scalar initializer
   93 |         .credentials = {
      |                        ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:93:24: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: error: field name not in record or union initializer
   97 |         .session = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:98:13: error: field name not in record or union initializer
   98 |             .keepalive = 60,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:98:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:13: error: field name not in record or union initializer
   99 |             .disable_clean_session = false,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:38: warning: excess elements in scalar initializer
   99 |             .disable_clean_session = false,
      |                                      ^~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:99:38: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:20: warning: excess elements in scalar initializer
   97 |         .session = {
      |                    ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:97:20: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: error: field name not in record or union initializer
  101 |         .network = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:102:13: error: field name not in record or union initializer
  102 |             .disable_auto_reconnect = false,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:102:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:13: error: field name not in record or union initializer
  103 |             .timeout_ms = 5000,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:27: warning: excess elements in scalar initializer
  103 |             .timeout_ms = 5000,
      |                           ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:103:27: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:20: warning: excess elements in scalar initializer
  101 |         .network = {
      |                    ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:101:20: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: error: field name not in record or union initializer
  105 |         .task = {
      |         ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: warning: braces around scalar initializer
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:9: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:106:13: error: field name not in record or union initializer
  106 |             .priority = 5,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:106:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:13: error: field name not in record or union initializer
  107 |             .stack_size = 6144,
      |             ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:13: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:27: warning: excess elements in scalar initializer
  107 |             .stack_size = 6144,
      |                           ^~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:107:27: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:17: warning: excess elements in scalar initializer
  105 |         .task = {
      |                 ^
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:105:17: note: (near initialization for 'mqtt_cfg')
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:111:19: error: implicit declaration of function 'esp_mqtt_client_init'; did you mean 'mqtt_client_init'? [-Wimplicit-function-declaration]
  111 |     mqtt_client = esp_mqtt_client_init(&mqtt_cfg);
      |                   ^~~~~~~~~~~~~~~~~~~~
      |                   mqtt_client_init
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:117:5: error: implicit declaration of function 'esp_mqtt_client_register_event' [-Wimplicit-function-declaration]
  117 |     esp_mqtt_client_register_event(mqtt_client, ESP_EVENT_ANY_ID, mqtt_event_handler, NULL);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_start':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:130:12: error: implicit declaration of function 'esp_mqtt_client_start'; did you mean 'mqtt_client_start'? [-Wimplicit-function-declaration]
  130 |     return esp_mqtt_client_start(mqtt_client);
      |            ^~~~~~~~~~~~~~~~~~~~~
      |            mqtt_client_start
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_client_stop':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:138:16: error: implicit declaration of function 'esp_mqtt_client_stop'; did you mean 'mqtt_client_stop'? [-Wimplicit-function-declaration]
  138 |         return esp_mqtt_client_stop(mqtt_client);
      |                ^~~~~~~~~~~~~~~~~~~~
      |                mqtt_client_stop
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: In function 'mqtt_publish_telemetry':
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:150:18: error: implicit declaration of function 'esp_mqtt_client_publish' [-Wimplicit-function-declaration]
  150 |     int msg_id = esp_mqtt_client_publish(mqtt_client, TB_TELEMETRY_TOPIC,
      |                  ^~~~~~~~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c: At top level:
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.c:245:6: error: conflicting types for 'mqtt_is_connected'; have '_Bool(void)'
  245 | bool mqtt_is_connected(void)
      |      ^~~~~~~~~~~~~~~~~
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main/mqtt_client.h:48:6: note: previous declaration of 'mqtt_is_connected' with type 'int(void)'
   48 | bool mqtt_is_connected(void);
      |      ^~~~~~~~~~~~~~~~~
[4/12] Performing build step for 'bootloader'
[1/111] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj
[2/111] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj
[3/111] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj
[4/111] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj
[5/111] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj
[6/111] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj
[7/111] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj
[8/111] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj
[9/111] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj
[10/111] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj
[11/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/esp_cpu_intr.c.obj
[12/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj
[13/111] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj
[14/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj
[15/111] Building C object esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj
[16/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/cpu_region_protect.c.obj
[17/111] Linking C static library esp-idf\log\liblog.a
[18/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj
[19/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj
[20/111] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj
[21/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj
[22/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj
[23/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj
[24/111] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj
[25/111] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj
[26/111] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj
[27/111] Linking C static library esp-idf\esp_rom\libesp_rom.a
[28/111] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj
[29/111] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj
[30/111] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj
[31/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj
[32/111] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj
[33/111] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj
[34/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj
[35/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj
[36/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj
[37/111] Linking C static library esp-idf\esp_common\libesp_common.a
[38/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj
[39/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj
[40/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj
[41/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj
[42/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj
[43/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj
[44/111] Linking C static library esp-idf\esp_hw_support\libesp_hw_support.a
[45/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj
[46/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj
[47/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj
[48/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj
[49/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj
[50/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj
[51/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj
[52/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_soc.c.obj
[53/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj
[54/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_sha.c.obj
[55/111] Linking C static library esp-idf\esp_system\libesp_system.a
[56/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj
[57/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj
[58/111] Building C object esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj
[59/111] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj
[60/111] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj
[61/111] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj
[62/111] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj
[63/111] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_esp32.c.obj
[64/111] Linking C static library esp-idf\efuse\libefuse.a
[65/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj
[66/111] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj
[67/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj
[68/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj
[69/111] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj
[70/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj
[71/111] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj
[72/111] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj
[73/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj
[74/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj
[75/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj
[76/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/emac_periph.c.obj
[77/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj
[78/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj
[79/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj
[80/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj
[81/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj
[82/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj
[83/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj
[84/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj
[85/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj
[86/111] Linking C static library esp-idf\bootloader_support\libbootloader_support.a
[87/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mpi_periph.c.obj
[88/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj
[89/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj
[90/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj
[91/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj
[92/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/wdt_periph.c.obj
[93/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/twai_periph.c.obj
[94/111] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj
[95/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj
[96/111] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj
[97/111] Generating project_elf_src_esp32.c
[98/111] Linking C static library esp-idf\esp_bootloader_format\libesp_bootloader_format.a
[99/111] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj
[100/111] Building C object CMakeFiles/bootloader.elf.dir/project_elf_src_esp32.c.obj
[101/111] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj
[102/111] Linking C static library esp-idf\spi_flash\libspi_flash.a
[103/111] Building C object esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj
[104/111] Linking C static library esp-idf\hal\libhal.a
[105/111] Linking C static library esp-idf\micro-ecc\libmicro-ecc.a
[106/111] Linking C static library esp-idf\soc\libsoc.a
[107/111] Linking C static library esp-idf\xtensa\libxtensa.a
[108/111] Linking C static library esp-idf\main\libmain.a
[109/111] Linking C executable bootloader.elf
[110/111] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32 image...

Merged 2 ELF sections

Successfully created esp32 image.

Generated C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/bootloader.bin
[111/111] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\kanagaraj\Sensor_project\AIOS_1\mesh_test\build\bootloader\esp-idf\esptool_py && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x1000 C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/bootloader.bin"

Bootloader binary size 0x6580 bytes. 0xa80 bytes (9%) free.


ninja: build stopped: subcommand failed.
