#include "thingsboard_client.h"
#include "data_router.h"
#include "esp_log.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "cJSON.h"
#include <string.h>

static const char *TAG = "THINGSBOARD_CLIENT";
static esp_mqtt_client_handle_t mqtt_client = NULL;
static bool mqtt_connected = false;
static QueueHandle_t mqtt_queue = NULL;

static void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data)
{
    esp_mqtt_event_handle_t event = event_data;
    esp_mqtt_client_handle_t client = event->client;
    
    switch ((esp_mqtt_event_id_t)event_id) {
    case MQTT_EVENT_CONNECTED:
        ESP_LOGI(TAG, "MQTT Connected to ThingsBoard");
        mqtt_connected = true;
        
        // Subscribe to RPC requests
        esp_mqtt_client_subscribe(client, TB_RPC_REQUEST_TOPIC, 1);
        ESP_LOGI(TAG, "Subscribed to RPC requests");
        
        // Subscribe to attribute responses
        esp_mqtt_client_subscribe(client, TB_ATTRIBUTES_RESPONSE_TOPIC, 1);
        ESP_LOGI(TAG, "Subscribed to attribute responses");
        
        break;
        
    case MQTT_EVENT_DISCONNECTED:
        ESP_LOGI(TAG, "MQTT Disconnected from ThingsBoard");
        mqtt_connected = false;
        break;
        
    case MQTT_EVENT_SUBSCRIBED:
        ESP_LOGI(TAG, "MQTT Subscribed, msg_id=%d", event->msg_id);
        break;
        
    case MQTT_EVENT_UNSUBSCRIBED:
        ESP_LOGI(TAG, "MQTT Unsubscribed, msg_id=%d", event->msg_id);
        break;
        
    case MQTT_EVENT_PUBLISHED:
        ESP_LOGI(TAG, "MQTT Published, msg_id=%d", event->msg_id);
        break;
        
    case MQTT_EVENT_DATA:
        ESP_LOGI(TAG, "MQTT Data received");
        ESP_LOGI(TAG, "Topic: %.*s", event->topic_len, event->topic);
        ESP_LOGI(TAG, "Data: %.*s", event->data_len, event->data);
        
        // Handle received MQTT data
        data_router_handle_mqtt_data(event->topic, event->topic_len, 
                                    event->data, event->data_len);
        break;
        
    case MQTT_EVENT_ERROR:
        ESP_LOGI(TAG, "MQTT Error");
        if (event->error_handle->error_type == MQTT_ERROR_TYPE_TCP_TRANSPORT) {
            ESP_LOGI(TAG, "Last errno string (%s)", strerror(event->error_handle->esp_transport_sock_errno));
        }
        break;
        
    default:
        ESP_LOGI(TAG, "Other MQTT event id:%d", event->event_id);
        break;
    }
}

esp_err_t thingsboard_client_init(void)
{
    ESP_LOGI(TAG, "Initializing MQTT client for ThingsBoard");
    
    // Create MQTT queue
    mqtt_queue = xQueueCreate(10, sizeof(mqtt_message_t));
    if (!mqtt_queue) {
        ESP_LOGE(TAG, "Failed to create MQTT queue");
        return ESP_ERR_NO_MEM;
    }
    
    // MQTT client configuration
    esp_mqtt_client_config_t mqtt_cfg = {
        .broker = {
            .address.hostname = THINGSBOARD_HOST,
            .broker.address.port = THINGSBOARD_PORT,
        },
        .credentials = {
            .username = THINGSBOARD_ACCESS_TOKEN,
            .authentication.password = NULL,
        },
        .session = {
            .keepalive = 60,
            .disable_clean_session = false,
        },
        .network = {
            .disable_auto_reconnect = false,
            .timeout_ms = 5000,
        },
        .task = {
            .priority = 5,
            .stack_size = 6144,
        },
    };
    
    mqtt_client = esp_mqtt_client_init(&mqtt_cfg);
    if (!mqtt_client) {
        ESP_LOGE(TAG, "Failed to initialize MQTT client");
        return ESP_FAIL;
    }
    
    esp_mqtt_client_register_event(mqtt_client, ESP_EVENT_ANY_ID, mqtt_event_handler, NULL);
    
    ESP_LOGI(TAG, "MQTT client initialized");
    return ESP_OK;
}

esp_err_t thingsboard_client_start(void)
{
    if (!mqtt_client) {
        ESP_ERROR_CHECK(thingsboard_client_init());
    }
    
    ESP_LOGI(TAG, "Starting MQTT client");
    return esp_mqtt_client_start(mqtt_client);
}

esp_err_t thingsboard_client_stop(void)
{
    if (mqtt_client) {
        ESP_LOGI(TAG, "Stopping MQTT client");
        mqtt_connected = false;
        return esp_mqtt_client_stop(mqtt_client);
    }
    return ESP_OK;
}

esp_err_t thingsboard_publish_telemetry(const char *json_data)
{
    if (!mqtt_connected || !mqtt_client) {
        ESP_LOGW(TAG, "MQTT not connected, cannot publish telemetry");
        return ESP_ERR_INVALID_STATE;
    }
    
    int msg_id = esp_mqtt_client_publish(mqtt_client, TB_TELEMETRY_TOPIC, 
                                        json_data, strlen(json_data), 1, 0);
    if (msg_id == -1) {
        ESP_LOGE(TAG, "Failed to publish telemetry");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Published telemetry, msg_id=%d", msg_id);
    return ESP_OK;
}

esp_err_t thingsboard_publish_attributes(const char *json_data)
{
    if (!mqtt_connected || !mqtt_client) {
        ESP_LOGW(TAG, "MQTT not connected, cannot publish attributes");
        return ESP_ERR_INVALID_STATE;
    }
    
    int msg_id = esp_mqtt_client_publish(mqtt_client, TB_ATTRIBUTES_TOPIC, 
                                        json_data, strlen(json_data), 1, 0);
    if (msg_id == -1) {
        ESP_LOGE(TAG, "Failed to publish attributes");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Published attributes, msg_id=%d", msg_id);
    return ESP_OK;
}

esp_err_t thingsboard_send_rpc_response(const char *request_id, const char *response)
{
    if (!mqtt_connected || !mqtt_client) {
        ESP_LOGW(TAG, "MQTT not connected, cannot send RPC response");
        return ESP_ERR_INVALID_STATE;
    }
    
    char topic[128];
    snprintf(topic, sizeof(topic), "%s%s", TB_RPC_RESPONSE_TOPIC, request_id);
    
    int msg_id = esp_mqtt_client_publish(mqtt_client, topic, 
                                        response, strlen(response), 1, 0);
    if (msg_id == -1) {
        ESP_LOGE(TAG, "Failed to send RPC response");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Sent RPC response, msg_id=%d", msg_id);
    return ESP_OK;
}

esp_err_t thingsboard_request_attributes(const char *client_keys, const char *shared_keys)
{
    if (!mqtt_connected || !mqtt_client) {
        ESP_LOGW(TAG, "MQTT not connected, cannot request attributes");
        return ESP_ERR_INVALID_STATE;
    }
    
    // Create request payload
    cJSON *json = cJSON_CreateObject();
    if (client_keys) {
        cJSON *client_array = cJSON_CreateString(client_keys);
        cJSON_AddItemToObject(json, "clientKeys", client_array);
    }
    if (shared_keys) {
        cJSON *shared_array = cJSON_CreateString(shared_keys);
        cJSON_AddItemToObject(json, "sharedKeys", shared_array);
    }
    
    char *json_string = cJSON_Print(json);
    cJSON_Delete(json);
    
    if (!json_string) {
        ESP_LOGE(TAG, "Failed to create attributes request JSON");
        return ESP_ERR_NO_MEM;
    }
    
    // Generate request ID
    static int request_counter = 0;
    char topic[128];
    snprintf(topic, sizeof(topic), "%s%d", TB_ATTRIBUTES_REQUEST_TOPIC, ++request_counter);
    
    int msg_id = esp_mqtt_client_publish(mqtt_client, topic, 
                                        json_string, strlen(json_string), 1, 0);
    
    free(json_string);
    
    if (msg_id == -1) {
        ESP_LOGE(TAG, "Failed to request attributes");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Requested attributes, msg_id=%d", msg_id);
    return ESP_OK;
}

bool thingsboard_is_connected(void)
{
    return mqtt_connected;
}
