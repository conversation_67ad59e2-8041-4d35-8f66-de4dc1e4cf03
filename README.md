# ESP32 WiFi Mesh with ThingsBoard IoT Cloud Integration

This project demonstrates an ESP32-based WiFi mesh network that sends sensor data to ThingsBoard IoT cloud platform via MQTT and distributes received cloud data to all mesh nodes.

## Features

- **WiFi Mesh Network**: Self-organizing mesh network with automatic root node selection
- **ThingsBoard Integration**: MQTT connection to ThingsBoard cloud platform
- **Bidirectional Communication**: Send telemetry data to cloud and receive commands/attributes
- **Dummy Sensor Data**: Generates realistic temperature, humidity, pressure, light, and battery data
- **Data Routing**: Intelligent routing between mesh nodes and cloud
- **Real-time Monitoring**: Heartbeat and status reporting
- **Configurable Parameters**: Easy configuration via menuconfig

## Hardware Requirements

- **ESP32 Development Boards** (minimum 2, recommended 3-6 for testing)
- **WiFi Router** with internet connection
- **Power Supply** for each ESP32 (USB or external)

## Software Requirements

- **ESP-IDF v4.4 or later**
- **ThingsBoard Account** (demo.thingsboard.io or your own instance)
- **Git** for cloning the repository

## Project Structure

```
esp_mesh_thingsboard/
├── main/
│   ├── main.c              # Main application entry point
│   ├── mesh_handler.c/h    # WiFi mesh network management
│   ├── mqtt_client.c/h     # MQTT client for ThingsBoard
│   ├── data_router.c/h     # Data routing and processing
│   └── CMakeLists.txt      # Component build configuration
├── CMakeLists.txt          # Project build configuration
├── sdkconfig.defaults      # Default SDK configuration
├── partitions.csv          # Partition table
├── Kconfig.projbuild       # Project configuration options
└── README.md               # This file
```

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd esp_mesh_thingsboard
```

### 2. Configure the Project

```bash
idf.py menuconfig
```

Navigate to "ESP Mesh ThingsBoard Configuration" and set:
- **WiFi SSID/Password**: Your router credentials
- **ThingsBoard Host**: demo.thingsboard.io (or your server)
- **ThingsBoard Access Token**: Your device access token
- **Mesh Network ID/Password**: Unique identifiers for your mesh

### 3. Build and Flash

```bash
# Build the project
idf.py build

# Flash to multiple ESP32 devices
idf.py -p /dev/ttyUSB0 flash monitor  # Device 1
idf.py -p /dev/ttyUSB1 flash monitor  # Device 2
# ... repeat for additional devices
```

### 4. Monitor Operation

```bash
idf.py monitor
```

## ThingsBoard Setup

### 1. Create Device

1. Login to ThingsBoard (demo.thingsboard.io)
2. Go to **Devices** → **Add Device**
3. Enter device name: "ESP32_Mesh_Node"
4. Copy the **Access Token** and update your configuration

### 2. Create Dashboard

1. Go to **Dashboards** → **Add Dashboard**
2. Add widgets for:
   - Temperature gauge
   - Humidity gauge
   - Pressure gauge
   - Light level chart
   - Battery level indicator
   - Node status table

### 3. Test RPC Commands

Send RPC commands from ThingsBoard:

```json
{
  "method": "getValue",
  "params": {}
}
```

```json
{
  "method": "broadcastMessage",
  "params": {
    "message": "Hello from cloud!"
  }
}
```

## Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| MESH_ID | "mesh_thingsboard" | Mesh network identifier |
| MESH_PASSWORD | "mesh_password" | Mesh network password |
| MESH_CHANNEL | 6 | WiFi channel (1-13) |
| MESH_MAX_LAYER | 6 | Maximum mesh layers |
| WIFI_SSID | "YOUR_WIFI_SSID" | Router SSID for root node |
| WIFI_PASSWORD | "YOUR_WIFI_PASSWORD" | Router password |
| THINGSBOARD_HOST | "demo.thingsboard.io" | ThingsBoard server |
| THINGSBOARD_PORT | 1883 | MQTT port |
| THINGSBOARD_ACCESS_TOKEN | "LgqL0Qn1v46tKJzKkZZf" | Device access token |
| DUMMY_DATA_INTERVAL | 30 | Data generation interval (seconds) |
| HEARTBEAT_INTERVAL | 60 | Status report interval (seconds) |

## Network Architecture

```
Internet
    |
WiFi Router
    |
Root Node (ESP32) ←→ ThingsBoard Cloud
    |
Mesh Network
    ├── Node 1 (ESP32)
    ├── Node 2 (ESP32)
    │   └── Node 3 (ESP32)
    └── Node 4 (ESP32)
        └── Node 5 (ESP32)
```

## Data Flow

### Uplink (Sensor → Cloud)
1. Non-root nodes generate sensor data
2. Data is sent via mesh to root node
3. Root node forwards data to ThingsBoard via MQTT
4. ThingsBoard stores and processes the data

### Downlink (Cloud → Sensors)
1. ThingsBoard sends RPC commands or attribute updates
2. Root node receives MQTT messages
3. Root node broadcasts data to all mesh nodes
4. Each node processes the received data

## Message Types

### Mesh Messages
- **MESH_MSG_SENSOR_DATA**: Sensor telemetry data
- **MESH_MSG_CLOUD_DATA**: Data from cloud to be distributed
- **MESH_MSG_COMMAND**: Commands to be executed
- **MESH_MSG_HEARTBEAT**: Node status and health

### MQTT Topics
- **Telemetry**: `v1/devices/me/telemetry`
- **Attributes**: `v1/devices/me/attributes`
- **RPC Requests**: `v1/devices/me/rpc/request/+`
- **RPC Responses**: `v1/devices/me/rpc/response/{requestId}`

## Troubleshooting

### Common Issues

1. **Mesh not forming**
   - Check MESH_ID and MESH_PASSWORD are same on all devices
   - Ensure devices are within range
   - Verify WiFi channel is available

2. **Root node not connecting to internet**
   - Check WiFi SSID/Password
   - Verify router has internet access
   - Check firewall settings

3. **MQTT connection failed**
   - Verify ThingsBoard host and port
   - Check access token is correct
   - Ensure internet connectivity

4. **No data in ThingsBoard**
   - Check device is online in ThingsBoard
   - Verify telemetry data format
   - Check MQTT topic names

### Debug Commands

```bash
# Monitor all devices
idf.py monitor

# Check mesh status
# Look for "Mesh started" and "Parent connected" messages

# Check MQTT status
# Look for "MQTT Connected to ThingsBoard" messages

# Verify data transmission
# Look for "Published telemetry" messages
```

## Extending the Project

### Adding Real Sensors

Replace dummy data generation in `data_router.c`:

```c
static void generate_real_sensor_data(sensor_data_t *data)
{
    // Read from actual sensors
    data->temperature = read_temperature_sensor();
    data->humidity = read_humidity_sensor();
    // ... etc
}
```

### Custom Commands

Add command processing in `data_router_handle_mesh_data()`:

```c
case MESH_MSG_COMMAND:
    if (strcmp(cmd->command, "led_on") == 0) {
        gpio_set_level(LED_PIN, 1);
    }
    break;
```

### Additional MQTT Topics

Subscribe to more topics in `mqtt_event_handler()`:

```c
case MQTT_EVENT_CONNECTED:
    esp_mqtt_client_subscribe(client, "custom/topic", 1);
    break;
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues and questions:
- Check the troubleshooting section
- Review ESP-IDF documentation
- Check ThingsBoard documentation
- Open an issue on GitHub
