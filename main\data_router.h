#ifndef DATA_ROUTER_H
#define DATA_ROUTER_H

#include "esp_err.h"
#include "esp_mesh.h"
#include "mesh_handler.h"

#ifdef __cplusplus
extern "C" {
#endif

// Data types
typedef enum {
    DATA_TYPE_SENSOR = 0,
    DATA_TYPE_COMMAND,
    DATA_TYPE_STATUS,
    DATA_TYPE_CONFIG
} data_type_t;

// Sensor data structure
typedef struct {
    float temperature;
    float humidity;
    float pressure;
    int light_level;
    int battery_level;
    uint32_t timestamp;
} sensor_data_t;

// Command structure
typedef struct {
    char command[32];
    char parameters[128];
    uint32_t timestamp;
} command_data_t;

// Status structure
typedef struct {
    char node_id[18];  // MAC address string
    int layer;
    bool is_root;
    int rssi;
    int free_heap;
    uint32_t uptime;
} status_data_t;

// Function declarations
esp_err_t data_router_init(void);
esp_err_t data_router_handle_mesh_data(mesh_message_t *msg, size_t msg_size, mesh_addr_t *from);
esp_err_t data_router_handle_mqtt_data(const char *topic, int topic_len, const char *data, int data_len);
esp_err_t data_router_send_sensor_data(sensor_data_t *sensor_data);
esp_err_t data_router_send_command(const char *command, const char *parameters);
esp_err_t data_router_broadcast_cloud_data(const char *data);
void data_router_start_dummy_data_task(void);
void data_router_stop_dummy_data_task(void);

#ifdef __cplusplus
}
#endif

#endif // DATA_ROUTER_H
