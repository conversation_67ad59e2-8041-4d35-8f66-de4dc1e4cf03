#include "data_router.h"
#include "mesh_handler.h"
#include "mqtt_client.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_random.h"
#include "esp_mesh.h"
#include "esp_wifi.h"
#include "esp_mac.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"
#include "cJSON.h"
#include <string.h>
#include <math.h>

// MAC address formatting macros
#ifndef MACSTR
#define MACSTR "%02x:%02x:%02x:%02x:%02x:%02x"
#endif

#ifndef MAC2STR
#define MAC2STR(a) (a)[0], (a)[1], (a)[2], (a)[3], (a)[4], (a)[5]
#endif

static const char *TAG = "DATA_ROUTER";
static TaskHandle_t dummy_data_task_handle = NULL;
static TimerHandle_t heartbeat_timer = NULL;

// Generate dummy sensor data
static void generate_dummy_sensor_data(sensor_data_t *data)
{
    // Generate realistic sensor values with some randomness
    static float temp_base = 25.0;
    static float hum_base = 60.0;
    static float press_base = 1013.25;
    
    // Add some variation
    temp_base += (esp_random() % 200 - 100) / 100.0; // ±1°C variation
    hum_base += (esp_random() % 100 - 50) / 10.0;    // ±5% variation
    press_base += (esp_random() % 200 - 100) / 100.0; // ±1 hPa variation
    
    // Keep values in realistic ranges
    if (temp_base < 15.0) temp_base = 15.0;
    if (temp_base > 35.0) temp_base = 35.0;
    if (hum_base < 30.0) hum_base = 30.0;
    if (hum_base > 90.0) hum_base = 90.0;
    if (press_base < 980.0) press_base = 980.0;
    if (press_base > 1040.0) press_base = 1040.0;
    
    data->temperature = temp_base;
    data->humidity = hum_base;
    data->pressure = press_base;
    data->light_level = esp_random() % 1024; // 0-1023 (ADC range)
    data->battery_level = 85 + (esp_random() % 15); // 85-100%
    data->timestamp = esp_log_timestamp();
}

// Convert sensor data to JSON
static char* sensor_data_to_json(sensor_data_t *data, const char *node_id)
{
    cJSON *json = cJSON_CreateObject();
    cJSON *temp = cJSON_CreateNumber(data->temperature);
    cJSON *hum = cJSON_CreateNumber(data->humidity);
    cJSON *press = cJSON_CreateNumber(data->pressure);
    cJSON *light = cJSON_CreateNumber(data->light_level);
    cJSON *battery = cJSON_CreateNumber(data->battery_level);
    cJSON *timestamp = cJSON_CreateNumber(data->timestamp);
    cJSON *node = cJSON_CreateString(node_id);
    
    cJSON_AddItemToObject(json, "temperature", temp);
    cJSON_AddItemToObject(json, "humidity", hum);
    cJSON_AddItemToObject(json, "pressure", press);
    cJSON_AddItemToObject(json, "light_level", light);
    cJSON_AddItemToObject(json, "battery_level", battery);
    cJSON_AddItemToObject(json, "timestamp", timestamp);
    cJSON_AddItemToObject(json, "node_id", node);
    
    char *json_string = cJSON_Print(json);
    cJSON_Delete(json);
    
    return json_string;
}

// Dummy data generation task
static void dummy_data_task(void *pvParameters)
{
    sensor_data_t sensor_data;
    char node_info[64];
    
    ESP_LOGI(TAG, "Starting dummy data generation task");
    
    while (1) {
        // Generate dummy sensor data
        generate_dummy_sensor_data(&sensor_data);
        
        // Get node information
        mesh_get_node_info(node_info, sizeof(node_info));
        
        // Send sensor data
        data_router_send_sensor_data(&sensor_data);
        
        ESP_LOGI(TAG, "Generated dummy data - Temp: %.1f°C, Humidity: %.1f%%, Pressure: %.1f hPa", 
                sensor_data.temperature, sensor_data.humidity, sensor_data.pressure);
        
        // Wait 30 seconds before next data generation
        vTaskDelay(pdMS_TO_TICKS(30000));
    }
}

// Heartbeat timer callback
static void heartbeat_timer_callback(TimerHandle_t xTimer)
{
    if (mesh_is_root_node()) {
        status_data_t status;
        mesh_addr_t id;
        esp_mesh_get_id(&id);
        
        // Fill status data
        snprintf(status.node_id, sizeof(status.node_id), MACSTR, MAC2STR(id.addr));
        status.layer = esp_mesh_get_layer();
        status.is_root = true;
        status.rssi = 0; // Root node doesn't have RSSI to parent
        status.free_heap = esp_get_free_heap_size();
        status.uptime = esp_log_timestamp();
        
        // Convert to JSON and send to cloud
        cJSON *json = cJSON_CreateObject();
        cJSON_AddStringToObject(json, "node_id", status.node_id);
        cJSON_AddNumberToObject(json, "layer", status.layer);
        cJSON_AddBoolToObject(json, "is_root", status.is_root);
        cJSON_AddNumberToObject(json, "rssi", status.rssi);
        cJSON_AddNumberToObject(json, "free_heap", status.free_heap);
        cJSON_AddNumberToObject(json, "uptime", status.uptime);
        
        char *json_string = cJSON_Print(json);
        cJSON_Delete(json);
        
        if (json_string) {
            mqtt_publish_attributes(json_string);
            free(json_string);
        }
    }
}

esp_err_t data_router_init(void)
{
    ESP_LOGI(TAG, "Initializing data router");
    
    // Create heartbeat timer (every 60 seconds)
    heartbeat_timer = xTimerCreate("heartbeat", pdMS_TO_TICKS(60000), pdTRUE, 
                                  NULL, heartbeat_timer_callback);
    if (!heartbeat_timer) {
        ESP_LOGE(TAG, "Failed to create heartbeat timer");
        return ESP_ERR_NO_MEM;
    }
    
    // Start heartbeat timer
    xTimerStart(heartbeat_timer, 0);
    
    // Start dummy data generation task
    data_router_start_dummy_data_task();
    
    ESP_LOGI(TAG, "Data router initialized");
    return ESP_OK;
}

esp_err_t data_router_handle_mesh_data(mesh_message_t *msg, size_t msg_size, mesh_addr_t *from)
{
    if (!msg || msg_size < sizeof(mesh_message_t)) {
        ESP_LOGE(TAG, "Invalid mesh message");
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Handling mesh data from " MACSTR ", type: %d",
            MAC2STR(from->addr), msg->type);

    switch (msg->type) {
    case MESH_MSG_SENSOR_DATA:
        if (msg->data_len >= sizeof(sensor_data_t)) {
            sensor_data_t *sensor_data = (sensor_data_t *)msg->data;

            // If this is the root node, send to cloud
            if (mesh_is_root_node()) {
                char node_id[18];
                snprintf(node_id, sizeof(node_id), MACSTR, MAC2STR(from->addr));

                char *json_string = sensor_data_to_json(sensor_data, node_id);
                if (json_string) {
                    mqtt_publish_telemetry(json_string);
                    free(json_string);
                }
            } else {
                // Forward to root node
                mesh_send_data((uint8_t *)msg, msg_size, msg->type);
            }
        }
        break;

    case MESH_MSG_CLOUD_DATA:
        // Data from cloud - broadcast to all mesh nodes
        ESP_LOGI(TAG, "Broadcasting cloud data: %.*s", msg->data_len, msg->data);
        // Process the cloud data locally and forward to children
        mesh_broadcast_data(msg->data, msg->data_len, MESH_MSG_CLOUD_DATA);
        break;

    case MESH_MSG_COMMAND:
        if (msg->data_len >= sizeof(command_data_t)) {
            command_data_t *cmd = (command_data_t *)msg->data;
            ESP_LOGI(TAG, "Received command: %s with parameters: %s",
                    cmd->command, cmd->parameters);

            // Process command locally
            // Add your command processing logic here

            // Forward command to children if needed
            mesh_broadcast_data(msg->data, msg->data_len, MESH_MSG_COMMAND);
        }
        break;

    case MESH_MSG_HEARTBEAT:
        ESP_LOGI(TAG, "Received heartbeat from " MACSTR "", MAC2STR(from->addr));
        break;

    default:
        ESP_LOGW(TAG, "Unknown mesh message type: %d", msg->type);
        break;
    }

    return ESP_OK;
}

esp_err_t data_router_handle_mqtt_data(const char *topic, int topic_len, const char *data, int data_len)
{
    ESP_LOGI(TAG, "Handling MQTT data from topic: %.*s", topic_len, topic);
    ESP_LOGI(TAG, "Data: %.*s", data_len, data);

    // Check if this is an RPC request
    if (strncmp(topic, "v1/devices/me/rpc/request/", 26) == 0) {
        // Extract request ID
        const char *request_id = topic + 26;

        // Parse RPC request
        cJSON *json = cJSON_ParseWithLength(data, data_len);
        if (json) {
            cJSON *method = cJSON_GetObjectItem(json, "method");
            cJSON *params = cJSON_GetObjectItem(json, "params");

            if (method && cJSON_IsString(method)) {
                ESP_LOGI(TAG, "RPC method: %s", method->valuestring);

                // Handle different RPC methods
                if (strcmp(method->valuestring, "getValue") == 0) {
                    // Return current sensor values
                    sensor_data_t sensor_data;
                    generate_dummy_sensor_data(&sensor_data);

                    cJSON *response = cJSON_CreateObject();
                    cJSON_AddNumberToObject(response, "temperature", sensor_data.temperature);
                    cJSON_AddNumberToObject(response, "humidity", sensor_data.humidity);
                    cJSON_AddNumberToObject(response, "pressure", sensor_data.pressure);

                    char *response_str = cJSON_Print(response);
                    cJSON_Delete(response);

                    if (response_str) {
                        mqtt_send_rpc_response(request_id, response_str);
                        free(response_str);
                    }
                } else if (strcmp(method->valuestring, "broadcastMessage") == 0) {
                    // Broadcast message to all mesh nodes
                    if (params && cJSON_IsObject(params)) {
                        cJSON *message = cJSON_GetObjectItem(params, "message");
                        if (message && cJSON_IsString(message)) {
                            data_router_broadcast_cloud_data(message->valuestring);

                            // Send success response
                            mqtt_send_rpc_response(request_id, "{\"success\": true}");
                        }
                    }
                }
            }

            cJSON_Delete(json);
        }
    }
    // Check if this is an attribute response
    else if (strncmp(topic, "v1/devices/me/attributes/response/", 34) == 0) {
        ESP_LOGI(TAG, "Received attribute response");
        // Process attribute data and potentially broadcast to mesh
        data_router_broadcast_cloud_data(data);
    }

    return ESP_OK;
}

esp_err_t data_router_send_sensor_data(sensor_data_t *sensor_data)
{
    if (!sensor_data) {
        return ESP_ERR_INVALID_ARG;
    }

    if (mesh_is_root_node()) {
        // Root node - send directly to cloud
        mesh_addr_t id;
        esp_mesh_get_id(&id);
        char node_id[18];
        snprintf(node_id, sizeof(node_id), MACSTR, MAC2STR(id.addr));

        char *json_string = sensor_data_to_json(sensor_data, node_id);
        if (json_string) {
            esp_err_t err = mqtt_publish_telemetry(json_string);
            free(json_string);
            return err;
        }
        return ESP_ERR_NO_MEM;
    } else {
        // Non-root node - send via mesh to root
        return mesh_send_data((uint8_t *)sensor_data, sizeof(sensor_data_t), MESH_MSG_SENSOR_DATA);
    }
}

esp_err_t data_router_send_command(const char *command, const char *parameters)
{
    if (!command) {
        return ESP_ERR_INVALID_ARG;
    }

    command_data_t cmd_data;
    strncpy(cmd_data.command, command, sizeof(cmd_data.command) - 1);
    cmd_data.command[sizeof(cmd_data.command) - 1] = '\0';

    if (parameters) {
        strncpy(cmd_data.parameters, parameters, sizeof(cmd_data.parameters) - 1);
    } else {
        cmd_data.parameters[0] = '\0';
    }
    cmd_data.parameters[sizeof(cmd_data.parameters) - 1] = '\0';
    cmd_data.timestamp = esp_log_timestamp();

    return mesh_broadcast_data((uint8_t *)&cmd_data, sizeof(cmd_data), MESH_MSG_COMMAND);
}

esp_err_t data_router_broadcast_cloud_data(const char *data)
{
    if (!data) {
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Broadcasting cloud data to mesh: %s", data);
    return mesh_broadcast_data((uint8_t *)data, strlen(data), MESH_MSG_CLOUD_DATA);
}

void data_router_start_dummy_data_task(void)
{
    if (dummy_data_task_handle == NULL) {
        xTaskCreate(dummy_data_task, "dummy_data", 4096, NULL, 5, &dummy_data_task_handle);
        ESP_LOGI(TAG, "Started dummy data generation task");
    }
}

void data_router_stop_dummy_data_task(void)
{
    if (dummy_data_task_handle != NULL) {
        vTaskDelete(dummy_data_task_handle);
        dummy_data_task_handle = NULL;
        ESP_LOGI(TAG, "Stopped dummy data generation task");
    }
}
