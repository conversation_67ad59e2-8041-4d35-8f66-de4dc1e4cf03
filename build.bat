@echo off
echo Setting up ESP-IDF environment...
call %IDF_PATH%\export.bat

echo Building ESP32 Mesh ThingsBoard project...
idf.py build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo To flash to device:
    echo   idf.py -p COM3 flash monitor
    echo   (Replace COM3 with your actual port)
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo Please check the error messages above.
)

pause
