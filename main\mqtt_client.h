#ifndef MQTT_CLIENT_H
#define MQTT_CLIENT_H

#include "esp_err.h"
#include "mqtt_client.h"

#ifdef __cplusplus
extern "C" {
#endif

// ThingsBoard configuration
#define THINGSBOARD_HOST        CONFIG_THINGSBOARD_HOST
#define THING<PERSON>OARD_PORT        CONFIG_THINGSBOARD_PORT
#define THINGSBOARD_ACCESS_TOKEN CONFIG_THINGSBOARD_ACCESS_TOKEN

// MQTT Topics
#define TB_TELEMETRY_TOPIC      "v1/devices/me/telemetry"
#define TB_ATTRIBUTES_TOPIC     "v1/devices/me/attributes"
#define TB_RPC_REQUEST_TOPIC    "v1/devices/me/rpc/request/+"
#define TB_RPC_RESPONSE_TOPIC   "v1/devices/me/rpc/response/"
#define TB_ATTRIBUTES_REQUEST_TOPIC "v1/devices/me/attributes/request/"
#define TB_ATTRIBUTES_RESPONSE_TOPIC "v1/devices/me/attributes/response/+"

// Message types for MQTT
typedef enum {
    MQTT_MSG_TELEMETRY = 0,
    MQTT_MSG_ATTRIBUTES,
    MQTT_MSG_RPC_RESPONSE,
    MQTT_MSG_ATTRIBUTES_REQUEST
} mqtt_msg_type_t;

// MQTT message structure
typedef struct {
    mqtt_msg_type_t type;
    char *topic;
    char *payload;
    int payload_len;
} mqtt_message_t;

// Function declarations
esp_err_t mqtt_client_init(void);
esp_err_t mqtt_client_start(void);
esp_err_t mqtt_client_stop(void);
esp_err_t mqtt_publish_telemetry(const char *json_data);
esp_err_t mqtt_publish_attributes(const char *json_data);
esp_err_t mqtt_send_rpc_response(const char *request_id, const char *response);
esp_err_t mqtt_request_attributes(const char *client_keys, const char *shared_keys);
bool mqtt_is_connected(void);

#ifdef __cplusplus
}
#endif

#endif // MQTT_CLIENT_H
