menu "ESP Mesh ThingsBoard Configuration"

    config MESH_ID
        string "Mesh Network ID"
        default "mesh_thingsboard"
        help
            Mesh network identifier. All devices in the same mesh network should use the same ID.

    config MESH_PASSWORD
        string "Mesh Network Password"
        default "mesh_password"
        help
            Password for the mesh network. All devices should use the same password.

    config MESH_CHANNEL
        int "Mesh WiFi Channel"
        range 1 13
        default 6
        help
            WiFi channel for the mesh network.

    config MESH_MAX_LAYER
        int "Maximum Mesh Layers"
        range 1 25
        default 6
        help
            Maximum number of layers in the mesh network.

    config WIFI_SSID
        string "WiFi SSID for Root Node"
        default "YOUR_WIFI_SSID"
        help
            SSID of the WiFi network that the root node will connect to for internet access.

    config WIFI_PASSWORD
        string "WiFi Password for Root Node"
        default "YOUR_WIFI_PASSWORD"
        help
            Password of the WiFi network that the root node will connect to.

    config THINGSBOARD_HOST
        string "ThingsBoard Server Host"
        default "demo.thingsboard.io"
        help
            Hostname or IP address of the ThingsBoard server.

    config THINGSBOARD_PORT
        int "ThingsBoard Server Port"
        range 1 65535
        default 1883
        help
            Port number for MQTT connection to ThingsBoard server.

    config THINGSBOARD_ACCESS_TOKEN
        string "ThingsBoard Device Access Token"
        default "LgqL0Qn1v46tKJzKkZZf"
        help
            Access token for the ThingsBoard device. Get this from your ThingsBoard device configuration.

    config DUMMY_DATA_INTERVAL
        int "Dummy Data Generation Interval (seconds)"
        range 5 3600
        default 30
        help
            Interval in seconds for generating dummy sensor data.

    config HEARTBEAT_INTERVAL
        int "Heartbeat Interval (seconds)"
        range 10 3600
        default 60
        help
            Interval in seconds for sending heartbeat/status messages.

endmenu
