#include "mesh_handler.h"
#include "thingsboard_client.h"
#include "data_router.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_mac.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

// MAC address formatting macros
#ifndef MACSTR
#define MACSTR "%02x:%02x:%02x:%02x:%02x:%02x"
#endif

#ifndef MAC2STR
#define MAC2STR(a) (a)[0], (a)[1], (a)[2], (a)[3], (a)[4], (a)[5]
#endif

#ifndef CONFIG_MESH_ROUTE_TABLE_SIZE
#define CONFIG_MESH_ROUTE_TABLE_SIZE 50
#endif

static const char *TAG = "MESH_HANDLER";
static bool is_mesh_connected = false;
static bool is_root = false;

// Mesh router configuration
static mesh_router_t mesh_router = {
    .ssid = WIFI_SSID,
    .password = WIFI_PASSWORD,
};

// Mesh configuration
static mesh_cfg_t mesh_cfg = {
    .channel = MESH_CHANNEL,
    .mesh_id = {{0}},  // Will be set in mesh_init()
    .mesh_ap = {
        .max_connection = MESH_AP_CONNECTIONS,
        .password = MESH_PASSWORD,
    },
    .crypto_funcs = &g_wifi_default_mesh_crypto_funcs,
};

static void mesh_event_handler(void *arg, esp_event_base_t event_base,
                              int32_t event_id, void *event_data)
{
    mesh_addr_t id = {0,};
    static uint16_t last_layer = 0;

    switch (event_id) {
    case MESH_EVENT_STARTED:
        esp_mesh_get_id(&id);
        ESP_LOGI(TAG, "Mesh started, ID:" MACSTR "", MAC2STR(id.addr));
        is_mesh_connected = esp_mesh_is_device_active();
        break;
        
    case MESH_EVENT_STOPPED:
        ESP_LOGI(TAG, "Mesh stopped");
        is_mesh_connected = false;
        break;
        
    case MESH_EVENT_CHILD_CONNECTED:
        ESP_LOGI(TAG, "Child connected");
        break;
        
    case MESH_EVENT_CHILD_DISCONNECTED:
        ESP_LOGI(TAG, "Child disconnected");
        break;
        
    case MESH_EVENT_ROUTING_TABLE_ADD:
        ESP_LOGI(TAG, "Routing table add");
        break;
        
    case MESH_EVENT_ROUTING_TABLE_REMOVE:
        ESP_LOGI(TAG, "Routing table remove");
        break;
        
    case MESH_EVENT_NO_PARENT_FOUND:
        ESP_LOGI(TAG, "No parent found");
        break;
        
    case MESH_EVENT_PARENT_CONNECTED:
        esp_mesh_get_id(&id);
        int layer = esp_mesh_get_layer();
        ESP_LOGI(TAG, "Parent connected, layer:%d, ID:" MACSTR "", layer, MAC2STR(id.addr));
        last_layer = layer;
        is_mesh_connected = true;
        
        if (esp_mesh_is_root()) {
            is_root = true;
            ESP_LOGI(TAG, "Root node - starting ThingsBoard client");
            thingsboard_client_start();
        }
        break;
        
    case MESH_EVENT_PARENT_DISCONNECTED:
        ESP_LOGI(TAG, "Parent disconnected");
        is_mesh_connected = false;
        is_root = false;
        break;
        
    case MESH_EVENT_LAYER_CHANGE:
        int new_layer = esp_mesh_get_layer();
        ESP_LOGI(TAG, "Layer changed: %d -> %d", last_layer, new_layer);
        last_layer = new_layer;
        
        if (esp_mesh_is_root()) {
            is_root = true;
            ESP_LOGI(TAG, "Became root node - starting ThingsBoard client");
            thingsboard_client_start();
        } else {
            is_root = false;
            thingsboard_client_stop();
        }
        break;
        
    case MESH_EVENT_ROOT_ADDRESS:
        ESP_LOGI(TAG, "Root address received");
        break;
        
    case MESH_EVENT_VOTE_STARTED:
        ESP_LOGI(TAG, "Vote started");
        break;
        
    case MESH_EVENT_VOTE_STOPPED:
        ESP_LOGI(TAG, "Vote stopped");
        break;
        
    case MESH_EVENT_ROOT_SWITCH_REQ:
        ESP_LOGI(TAG, "Root switch request");
        break;
        
    case MESH_EVENT_ROOT_SWITCH_ACK:
        ESP_LOGI(TAG, "Root switch ack");
        break;
        
    case MESH_EVENT_TODS_STATE:
        ESP_LOGI(TAG, "ToDS state changed");
        break;
        
    case MESH_EVENT_ROOT_FIXED:
        ESP_LOGI(TAG, "Root fixed");
        break;
        
    case MESH_EVENT_ROOT_ASKED_YIELD:
        ESP_LOGI(TAG, "Root asked yield");
        break;
        
    case MESH_EVENT_CHANNEL_SWITCH:
        ESP_LOGI(TAG, "Channel switch");
        break;
        
    case MESH_EVENT_SCAN_DONE:
        ESP_LOGI(TAG, "Scan done");
        break;
        
    case MESH_EVENT_NETWORK_STATE:
        ESP_LOGI(TAG, "Network state changed");
        break;
        
    case MESH_EVENT_STOP_RECONNECTION:
        ESP_LOGI(TAG, "Stop reconnection");
        break;
        
    case MESH_EVENT_FIND_NETWORK:
        ESP_LOGI(TAG, "Find network");
        break;
        
    case MESH_EVENT_ROUTER_SWITCH:
        ESP_LOGI(TAG, "Router switch");
        break;
        
    default:
        ESP_LOGI(TAG, "Unknown mesh event: %ld", event_id);
        break;
    }
}

static void mesh_receive_task(void *arg)
{
    mesh_addr_t from;
    mesh_data_t data;
    int flag = 0;
    esp_err_t err;

    while (1) {
        data.data = malloc(MESH_MPS);
        data.size = MESH_MPS;

        err = esp_mesh_recv(&from, &data, portMAX_DELAY, &flag, NULL, 0);
        if (err != ESP_OK || !data.size) {
            ESP_LOGE(TAG, "Mesh receive error: %s", esp_err_to_name(err));
            free(data.data);
            continue;
        }

        ESP_LOGI(TAG, "Received %d bytes from " MACSTR "", data.size, MAC2STR(from.addr));

        // Process received mesh message
        if (data.size >= sizeof(mesh_message_t)) {
            mesh_message_t *msg = (mesh_message_t *)data.data;
            data_router_handle_mesh_data(msg, data.size, &from);
        }

        free(data.data);
    }
}

esp_err_t mesh_init(void)
{
    ESP_LOGI(TAG, "Initializing mesh network");

    // Initialize WiFi
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    wifi_init_config_t config = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&config));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &mesh_event_handler, NULL));
    ESP_ERROR_CHECK(esp_wifi_set_storage(WIFI_STORAGE_FLASH));
    ESP_ERROR_CHECK(esp_wifi_start());

    // Initialize mesh
    ESP_ERROR_CHECK(esp_mesh_init());
    ESP_ERROR_CHECK(esp_event_handler_register(MESH_EVENT, ESP_EVENT_ANY_ID, &mesh_event_handler, NULL));

    // Set mesh ID
    strncpy((char*)mesh_cfg.mesh_id.addr, MESH_ID, sizeof(mesh_cfg.mesh_id.addr) - 1);
    mesh_cfg.mesh_id.addr[sizeof(mesh_cfg.mesh_id.addr) - 1] = '\0';

    // Set mesh configuration
    ESP_ERROR_CHECK(esp_mesh_set_max_layer(MESH_MAX_LAYER));
    ESP_ERROR_CHECK(esp_mesh_set_vote_percentage(1));
    ESP_ERROR_CHECK(esp_mesh_set_xon_qsize(128));
    ESP_ERROR_CHECK(esp_mesh_allow_root_conflicts(false));
    ESP_ERROR_CHECK(esp_mesh_set_ap_assoc_expire(10));
    ESP_ERROR_CHECK(esp_mesh_set_announce_interval(600, 3300));

    // Set WiFi configuration for root node
    ESP_ERROR_CHECK(esp_mesh_set_ap_authmode(WIFI_AUTH_WPA2_PSK));
    ESP_ERROR_CHECK(esp_mesh_set_config(&mesh_cfg));

    // Set router configuration for root node
    ESP_ERROR_CHECK(esp_mesh_set_router(&mesh_router));

    // Start mesh
    ESP_ERROR_CHECK(esp_mesh_start());

    // Create mesh receive task
    xTaskCreate(mesh_receive_task, "mesh_receive", 4096, NULL, 5, NULL);

    ESP_LOGI(TAG, "Mesh network initialized");
    return ESP_OK;
}

esp_err_t mesh_send_data(const uint8_t *data, size_t len, mesh_msg_type_t type)
{
    if (!is_mesh_connected) {
        ESP_LOGW(TAG, "Mesh not connected, cannot send data");
        return ESP_ERR_MESH_NOT_START;
    }

    // Create mesh message
    size_t msg_size = sizeof(mesh_message_t) + len;
    mesh_message_t *msg = malloc(msg_size);
    if (!msg) {
        ESP_LOGE(TAG, "Failed to allocate memory for mesh message");
        return ESP_ERR_NO_MEM;
    }

    msg->type = type;
    msg->timestamp = esp_log_timestamp();
    msg->data_len = len;
    memcpy(msg->data, data, len);

    mesh_data_t mesh_data = {
        .data = (uint8_t *)msg,
        .size = msg_size,
        .proto = MESH_PROTO_BIN,
        .tos = MESH_TOS_P2P,
    };

    esp_err_t err;
    if (is_root) {
        // Root node - send to MQTT
        err = ESP_OK; // Will be handled by data router
    } else {
        // Send to root node
        mesh_addr_t root_addr;
        esp_mesh_get_id(&root_addr); // Get our own ID first, then find root
        err = esp_mesh_send(&root_addr, &mesh_data, MESH_DATA_P2P, NULL, 0);
    }

    free(msg);
    return err;
}

esp_err_t mesh_broadcast_data(const uint8_t *data, size_t len, mesh_msg_type_t type)
{
    if (!is_mesh_connected) {
        ESP_LOGW(TAG, "Mesh not connected, cannot broadcast data");
        return ESP_ERR_MESH_NOT_START;
    }

    // Create mesh message
    size_t msg_size = sizeof(mesh_message_t) + len;
    mesh_message_t *msg = malloc(msg_size);
    if (!msg) {
        ESP_LOGE(TAG, "Failed to allocate memory for mesh message");
        return ESP_ERR_NO_MEM;
    }

    msg->type = type;
    msg->timestamp = esp_log_timestamp();
    msg->data_len = len;
    memcpy(msg->data, data, len);

    mesh_data_t mesh_data = {
        .data = (uint8_t *)msg,
        .size = msg_size,
        .proto = MESH_PROTO_BIN,
        .tos = MESH_TOS_P2P,
    };

    // Get routing table and send to all nodes
    mesh_addr_t route_table[CONFIG_MESH_ROUTE_TABLE_SIZE];
    int route_table_size = 0;

    esp_err_t err_route = esp_mesh_get_routing_table(route_table,
                                                    CONFIG_MESH_ROUTE_TABLE_SIZE,
                                                    &route_table_size);
    if (err_route != ESP_OK) {
        ESP_LOGW(TAG, "Failed to get routing table: %s", esp_err_to_name(err_route));
        free(msg);
        return err_route;
    }

    esp_err_t err = ESP_OK;
    for (int i = 0; i < route_table_size; i++) {
        esp_err_t send_err = esp_mesh_send(&route_table[i], &mesh_data, MESH_DATA_P2P, NULL, 0);
        if (send_err != ESP_OK) {
            ESP_LOGW(TAG, "Failed to send to node " MACSTR ": %s",
                    MAC2STR(route_table[i].addr), esp_err_to_name(send_err));
            err = send_err;
        }
    }
    free(msg);
    return err;
}

bool mesh_is_root_node(void)
{
    return is_root;
}

void mesh_get_node_info(char *info_str, size_t max_len)
{
    mesh_addr_t id;
    esp_mesh_get_id(&id);
    int layer = esp_mesh_get_layer();

    snprintf(info_str, max_len,
             "Node ID: " MACSTR ", Layer: %d, Root: %s, Connected: %s",
             MAC2STR(id.addr), layer,
             is_root ? "Yes" : "No",
             is_mesh_connected ? "Yes" : "No");
}
