[0/1] Re-running CMake...
-- ccache will be used for faster recompilation
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32
-- Project sdkconfig file C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig
Loading defaults file C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults...
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults:4 CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE was replaced with CONFIG_ESP_COEX_SW_COEXIST_ENABLE 
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults:5 CONFIG_ESP32_WIFI_NVS_ENABLED was replaced with CONFIG_ESP_WIFI_NVS_ENABLED 
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults:6 CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_1 was replaced with CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_1 
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults:7 CONFIG_ESP32_WIFI_MGMT_SBUF_NUM was replaced with CONFIG_ESP_WIFI_MGMT_SBUF_NUM 
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults:8 CONFIG_ESP32_WIFI_IRAM_OPT was replaced with CONFIG_ESP_WIFI_IRAM_OPT 
C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults:9 CONFIG_ESP32_WIFI_RX_IRAM_OPT was replaced with CONFIG_ESP_WIFI_RX_IRAM_OPT 
warning: unknown kconfig symbol 'ESP_WIFI_MESH_MAX_LAYER' assigned to '6' in C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP_WIFI_MESH_MAX_CONNECTIONS' assigned to '10' in C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults
warning: unknown kconfig symbol 'MESH_ROUTE_TABLE_SIZE' assigned to '50' in C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP_MQTT_TASK_STACK_SIZE' assigned to '6144' in C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults
warning: unknown kconfig symbol 'JSON_MAXIMUM_NESTING' assigned to '10' in C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- App "esp_mesh_thingsboard" version: 1
-- Adding linker script C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.api.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.libgcc.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.newlib-data.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.syscalls.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32/ld/esp32.rom.newlib-funcs.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32/ld/esp32.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: C:/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace C:/Espressif/frameworks/esp-idf-v5.4.1/components/app_update C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader C:/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support C:/Espressif/frameworks/esp-idf-v5.4.1/components/bt C:/Espressif/frameworks/esp-idf-v5.4.1/components/cmock C:/Espressif/frameworks/esp-idf-v5.4.1/components/console C:/Espressif/frameworks/esp-idf-v5.4.1/components/cxx C:/Espressif/frameworks/esp-idf-v5.4.1/components/driver C:/Espressif/frameworks/esp-idf-v5.4.1/components/efuse C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ana_cmpr C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_dac C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_isp C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_jpeg C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_parlio C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ppa C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdio C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_touch_sens C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif_stack C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_vfs_console C:/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi C:/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump C:/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py C:/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs C:/Espressif/frameworks/esp-idf-v5.4.1/components/freertos C:/Espressif/frameworks/esp-idf-v5.4.1/components/hal C:/Espressif/frameworks/esp-idf-v5.4.1/components/heap C:/Espressif/frameworks/esp-idf-v5.4.1/components/http_parser C:/Espressif/frameworks/esp-idf-v5.4.1/components/idf_test C:/Espressif/frameworks/esp-idf-v5.4.1/components/ieee802154 C:/Espressif/frameworks/esp-idf-v5.4.1/components/json C:/Espressif/frameworks/esp-idf-v5.4.1/components/log C:/Espressif/frameworks/esp-idf-v5.4.1/components/lwip C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/main C:/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls C:/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt C:/Espressif/frameworks/esp-idf-v5.4.1/components/newlib C:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash C:/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider C:/Espressif/frameworks/esp-idf-v5.4.1/components/openthread C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table C:/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon C:/Espressif/frameworks/esp-idf-v5.4.1/components/protobuf-c C:/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm C:/Espressif/frameworks/esp-idf-v5.4.1/components/pthread C:/Espressif/frameworks/esp-idf-v5.4.1/components/rt C:/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc C:/Espressif/frameworks/esp-idf-v5.4.1/components/soc C:/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash C:/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs C:/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport C:/Espressif/frameworks/esp-idf-v5.4.1/components/ulp C:/Espressif/frameworks/esp-idf-v5.4.1/components/unity C:/Espressif/frameworks/esp-idf-v5.4.1/components/usb C:/Espressif/frameworks/esp-idf-v5.4.1/components/vfs C:/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling C:/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning C:/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant C:/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa
-- Configuring done (16.6s)
-- Generating done (5.2s)
-- Build files have been written to: C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build
[1/14] Performing build step for 'bootloader'
[1/1] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\kanagaraj\Sensor_project\AIOS_1\mesh_test\build\bootloader\esp-idf\esptool_py && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x1000 C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/bootloader/bootloader.bin"

Bootloader binary size 0x6580 bytes. 0xa80 bytes (9%) free.


[2/14] No install step for 'bootloader'
[3/14] Completed 'bootloader'
[4/14] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
[5/14] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/thingsboard_client.c.obj
[6/14] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/mesh_handler.c.obj
[7/14] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/data_router.c.obj
[8/14] Linking C static library esp-idf\main\libmain.a
[9/14] Generating ld/sections.ld
[10/14] Building C object CMakeFiles/esp_mesh_thingsboard.elf.dir/project_elf_src_esp32.c.obj
[11/14] Linking CXX executable esp_mesh_thingsboard.elf
[12/14] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32 image...

Merged 2 ELF sections

Successfully created esp32 image.

Generated C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp_mesh_thingsboard.bin
[13/14] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\kanagaraj\Sensor_project\AIOS_1\mesh_test\build\esp-idf\esptool_py && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/check_sizes.py --offset 0x8000 partition --type app C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/partition_table/partition-table.bin C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp_mesh_thingsboard.bin"
FAILED: esp-idf/esptool_py/CMakeFiles/app_check_size C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp-idf/esptool_py/CMakeFiles/app_check_size 
C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\kanagaraj\Sensor_project\AIOS_1\mesh_test\build\esp-idf\esptool_py && C:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/check_sizes.py --offset 0x8000 partition --type app C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/partition_table/partition-table.bin C:/Users/<USER>/kanagaraj/Sensor_project/AIOS_1/mesh_test/build/esp_mesh_thingsboard.bin"
Error: app partition is too small for binary esp_mesh_thingsboard.bin size 0x10ace0:

  - Part 'factory' 0/0 @ 0x10000 size 0x100000 (overflow 0xace0)

ninja: build stopped: subcommand failed.
