# Test Node Instructions

This test code is designed to verify mesh connectivity and data transmission from child nodes (non-root nodes) in your ESP32 mesh network.

## What the Test Node Does

1. **Connects to Mesh Network**: Joins the existing mesh using the same credentials
2. **Generates Test Data**: Creates dummy sensor data with sequence numbers
3. **Sends Data to Root**: Transmits sensor data via mesh to the root node
4. **Reports Status**: Logs connection status, layer information, and statistics
5. **Receives Messages**: Listens for messages from other mesh nodes

## How to Use

### Option 1: Replace Main Code Temporarily
```bash
# Backup your current main.c
cp main/main.c main/main.c.backup

# Copy test node code
cp test_node.c main/main.c

# Update CMakeLists.txt
cp test_CMakeLists.txt main/CMakeLists.txt

# Build and flash
idf.py build
idf.py -p /dev/ttyUSB1 flash monitor
```

### Option 2: Create Separate Test Project
```bash
# Create new directory
mkdir ../mesh_test_node
cd ../mesh_test_node

# Copy project structure
cp ../mesh_test/CMakeLists.txt .
cp ../mesh_test/sdkconfig.defaults .
cp ../mesh_test/partitions.csv .

# Create main directory and copy test code
mkdir main
cp ../mesh_test/test_node.c main/main.c
cp ../mesh_test/test_CMakeLists.txt main/CMakeLists.txt

# Build and flash
idf.py build
idf.py -p /dev/ttyUSB1 flash monitor
```

## Expected Output

### When Starting:
```
I (xxx) TEST_NODE: === ESP32 MESH TEST NODE STARTING ===
I (xxx) TEST_NODE: TEST NODE - Initializing mesh network
I (xxx) TEST_NODE: TEST NODE - Mesh network initialized
I (xxx) TEST_NODE: === TEST NODE INITIALIZED SUCCESSFULLY ===
```

### When Connecting to Mesh:
```
I (xxx) TEST_NODE: TEST NODE - Mesh started, ID:xx:xx:xx:xx:xx:xx
I (xxx) TEST_NODE: TEST NODE - Parent connected, layer:2, ID:xx:xx:xx:xx:xx:xx
I (xxx) TEST_NODE: TEST NODE - Connected as child node at layer 2
I (xxx) TEST_NODE: TEST NODE - Mesh connected, starting data transmission
```

### During Data Transmission:
```
I (xxx) TEST_NODE: TEST NODE - Generated data: temp=23.4°C, hum=65.2%, node=123, seq=1
I (xxx) TEST_NODE: Test message sent to root: type=1, size=24
I (xxx) TEST_NODE: TEST NODE - Generated data: temp=24.1°C, hum=63.8%, node=123, seq=2
I (xxx) TEST_NODE: Test message sent to root: type=1, size=24
```

### Status Reports (every 30 seconds):
```
I (xxx) TEST_NODE: === TEST NODE STATUS ===
I (xxx) TEST_NODE: Node ID: xx:xx:xx:xx:xx:xx
I (xxx) TEST_NODE: Layer: 2
I (xxx) TEST_NODE: Is Root: NO
I (xxx) TEST_NODE: Connected: YES
I (xxx) TEST_NODE: Free Heap: 234567 bytes
I (xxx) TEST_NODE: Uptime: 45678 ms
I (xxx) TEST_NODE: Messages Sent: 5
I (xxx) TEST_NODE: ========================
```

## Test Features

### 1. Sensor Data Generation
- **Temperature**: 15-35°C with realistic variations
- **Humidity**: 30-90% with realistic variations
- **Node ID**: Random identifier for tracking
- **Sequence Number**: Incremental counter for message tracking

### 2. Message Types
- **TEST_MSG_SENSOR_DATA**: Regular sensor data transmission
- **TEST_MSG_PING**: Periodic ping messages (every 5th transmission)
- **TEST_MSG_STATUS**: Status information

### 3. Timing
- **Data Transmission**: Every 15 seconds
- **Status Report**: Every 30 seconds
- **Ping Messages**: Every 5th data transmission (75 seconds)

## Configuration

Make sure these values match your main project:

```c
#define MESH_ID                 "mesh_thingsboard"
#define MESH_PASSWORD           "mesh_password"
#define MESH_CHANNEL            6
#define WIFI_SSID               "YOUR_WIFI_SSID"      // Only used if becomes root
#define WIFI_PASSWORD           "YOUR_WIFI_PASSWORD"  // Only used if becomes root
```

## Testing Scenarios

### 1. Basic Connectivity Test
1. Start main project on one ESP32 (becomes root)
2. Start test node on second ESP32 (becomes child)
3. Verify mesh formation and data transmission

### 2. Multi-Layer Test
1. Start main project (root)
2. Start test node 1 (layer 2)
3. Start test node 2 (layer 3, connects through node 1)
4. Verify data flows through multiple layers

### 3. Root Failover Test
1. Start multiple test nodes
2. Disconnect root node
3. Verify new root election and reconnection

### 4. Data Reception Test
1. Send RPC commands from ThingsBoard
2. Verify test nodes receive and log the commands
3. Check data distribution across mesh

## Troubleshooting

### Node Not Connecting
```
E (xxx) TEST_NODE: TEST NODE - Mesh receive error: ESP_ERR_MESH_NOT_START
```
**Solution**: Check mesh credentials match main project

### No Data Transmission
```
W (xxx) TEST_NODE: Mesh not connected, cannot send message
```
**Solution**: Wait for mesh connection, check parent node availability

### Memory Issues
```
E (xxx) TEST_NODE: Failed to allocate memory for test message
```
**Solution**: Reduce message frequency or size, check for memory leaks

## Integration with Main Project

The test node is designed to work alongside your main mesh project. When the main project receives test messages, you should see logs like:

```
I (xxx) DATA_ROUTER: Handling mesh data from xx:xx:xx:xx:xx:xx, type: 1
I (xxx) MQTT_CLIENT: Published telemetry, msg_id=123
```

This confirms that:
1. Test node successfully sent data via mesh
2. Root node received the data
3. Data was forwarded to ThingsBoard cloud

## Monitoring Multiple Nodes

To monitor multiple test nodes simultaneously:

```bash
# Terminal 1 - Main project (root)
idf.py -p /dev/ttyUSB0 monitor

# Terminal 2 - Test node 1
idf.py -p /dev/ttyUSB1 monitor

# Terminal 3 - Test node 2
idf.py -p /dev/ttyUSB2 monitor
```

Look for message sequence numbers and timestamps to track data flow across the network.
